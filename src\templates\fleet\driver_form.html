<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:driver_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- المعلومات الشخصية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-user"></i> المعلومات الشخصية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.first_name.id_for_label }}" class="form-label">{{ form.first_name.label }} <span class="text-danger">*</span></label>
                                            {{ form.first_name }}
                                            {% if form.first_name.errors %}
                                                <div class="text-danger">{{ form.first_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.last_name.id_for_label }}" class="form-label">{{ form.last_name.label }} <span class="text-danger">*</span></label>
                                            {{ form.last_name }}
                                            {% if form.last_name.errors %}
                                                <div class="text-danger">{{ form.last_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.national_id.id_for_label }}" class="form-label">{{ form.national_id.label }} <span class="text-danger">*</span></label>
                                            {{ form.national_id }}
                                            {% if form.national_id.errors %}
                                                <div class="text-danger">{{ form.national_id.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.birth_date.id_for_label }}" class="form-label">{{ form.birth_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.birth_date }}
                                            {% if form.birth_date.errors %}
                                                <div class="text-danger">{{ form.birth_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }} <span class="text-danger">*</span></label>
                                            {{ form.phone }}
                                            {% if form.phone.errors %}
                                                <div class="text-danger">{{ form.phone.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                            {{ form.email }}
                                            {% if form.email.errors %}
                                                <div class="text-danger">{{ form.email.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }} <span class="text-danger">*</span></label>
                                        {{ form.address }}
                                        {% if form.address.errors %}
                                            <div class="text-danger">{{ form.address.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الرخصة -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-id-card"></i> معلومات الرخصة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.license_number.id_for_label }}" class="form-label">{{ form.license_number.label }} <span class="text-danger">*</span></label>
                                            {{ form.license_number }}
                                            {% if form.license_number.errors %}
                                                <div class="text-danger">{{ form.license_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.license_type.id_for_label }}" class="form-label">{{ form.license_type.label }} <span class="text-danger">*</span></label>
                                            {{ form.license_type }}
                                            {% if form.license_type.errors %}
                                                <div class="text-danger">{{ form.license_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.license_issue_date.id_for_label }}" class="form-label">{{ form.license_issue_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.license_issue_date }}
                                            {% if form.license_issue_date.errors %}
                                                <div class="text-danger">{{ form.license_issue_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.license_expiry_date.id_for_label }}" class="form-label">{{ form.license_expiry_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.license_expiry_date }}
                                            {% if form.license_expiry_date.errors %}
                                                <div class="text-danger">{{ form.license_expiry_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العمل -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-briefcase"></i> معلومات العمل</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.hire_date.id_for_label }}" class="form-label">{{ form.hire_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.hire_date }}
                                            {% if form.hire_date.errors %}
                                                <div class="text-danger">{{ form.hire_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                            {{ form.status }}
                                            {% if form.status.errors %}
                                                <div class="text-danger">{{ form.status.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.salary.id_for_label }}" class="form-label">{{ form.salary.label }}</label>
                                        {{ form.salary }}
                                        {% if form.salary.errors %}
                                            <div class="text-danger">{{ form.salary.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'fleet:driver_list' %}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ submit_text }}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> إرشادات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> رقم الهوية يجب أن يكون 10 أرقام</li>
                                        <li><i class="fas fa-check text-success"></i> رقم الهاتف يبدأ بـ 05</li>
                                        <li><i class="fas fa-check text-success"></i> رقم الرخصة يجب أن يكون فريد</li>
                                        <li><i class="fas fa-check text-success"></i> تأكد من صحة تاريخ انتهاء الرخصة</li>
                                        <li><i class="fas fa-check text-success"></i> الراتب اختياري</li>
                                    </ul>
                                </div>
                            </div>

                            {% if driver %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-history"></i> إجراءات إضافية</h6>
                                </div>
                                <div class="card-body">
                                    <a href="{% url 'fleet:driver_detail' driver.pk %}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                    <a href="{% url 'fleet:assignment_create' %}" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-plus"></i> تعيين مركبة
                                    </a>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
