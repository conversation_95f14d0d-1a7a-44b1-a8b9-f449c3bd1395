<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل السائق - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تفاصيل السائق: {{ driver.full_name }}</h1>
                    <div>
                        <a href="{% url 'fleet:driver_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <a href="{% url 'fleet:driver_edit' driver.pk %}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                    </div>
                </div>

                <!-- معلومات السائق الأساسية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-user"></i> معلومات شخصية</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td>{{ driver.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>رقم الهوية:</strong></td>
                                        <td>{{ driver.national_id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ الميلاد:</strong></td>
                                        <td>{{ driver.birth_date }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>رقم الهاتف:</strong></td>
                                        <td>{{ driver.phone }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>البريد الإلكتروني:</strong></td>
                                        <td>{{ driver.email|default:"غير محدد" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>العنوان:</strong></td>
                                        <td>{{ driver.address }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-id-card"></i> معلومات الرخصة</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>رقم الرخصة:</strong></td>
                                        <td>{{ driver.license_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>نوع الرخصة:</strong></td>
                                        <td>{{ driver.get_license_type_display }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ الإصدار:</strong></td>
                                        <td>{{ driver.license_issue_date }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ الانتهاء:</strong></td>
                                        <td>
                                            {{ driver.license_expiry_date }}
                                            {% if driver.is_license_expired %}
                                                <span class="badge bg-danger">منتهية</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>{{ driver.get_status_display }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-briefcase"></i> معلومات العمل</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>تاريخ التوظيف:</strong><br>
                                {{ driver.hire_date }}
                            </div>
                            <div class="col-md-4">
                                <strong>الراتب:</strong><br>
                                {{ driver.salary|default:"غير محدد" }}
                            </div>
                            <div class="col-md-4">
                                <strong>المركبة الحالية:</strong><br>
                                {% if current_assignment %}
                                    <a href="{% url 'fleet:vehicle_detail' current_assignment.vehicle.pk %}">
                                        {{ current_assignment.vehicle.plate_number }}
                                    </a>
                                    <small class="text-muted">(منذ {{ current_assignment.start_date|date:"Y-m-d" }})</small>
                                {% else %}
                                    <span class="text-muted">لا يوجد تعيين حالي</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تاريخ تعيينات المركبات -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> تاريخ تعيينات المركبات</h5>
                    </div>
                    <div class="card-body">
                        {% if assignments %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المركبة</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>المدة</th>
                                            <th>الحالة</th>
                                            <th>البيان</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for assignment in assignments %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'fleet:vehicle_detail' assignment.vehicle.pk %}">
                                                    {{ assignment.vehicle.plate_number }}
                                                </a>
                                            </td>
                                            <td>{{ assignment.start_date|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                {% if assignment.end_date %}
                                                    {{ assignment.end_date|date:"Y-m-d H:i" }}
                                                {% else %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if assignment.end_date %}
                                                    {{ assignment.duration.days }} يوم
                                                {% else %}
                                                    {{ assignment.duration.days }} يوم (مستمر)
                                                {% endif %}
                                            </td>
                                            <td>{{ assignment.get_status_display }}</td>
                                            <td>{{ assignment.notes|default:"لا يوجد" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد تعيينات مسجلة</p>
                        {% endif %}
                    </div>
                </div>

                <!-- الرحلات الأخيرة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-route"></i> الرحلات الأخيرة</h5>
                    </div>
                    <div class="card-body">
                        {% if trips %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>من</th>
                                            <th>إلى</th>
                                            <th>تاريخ البداية</th>
                                            <th>الحالة</th>
                                            <th>المركبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for trip in trips %}
                                        <tr>
                                            <td>{{ trip.start_location }}</td>
                                            <td>{{ trip.end_location }}</td>
                                            <td>{{ trip.start_time|date:"Y-m-d H:i" }}</td>
                                            <td>{{ trip.get_status_display }}</td>
                                            <td>{{ trip.vehicle.plate_number }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد رحلات مسجلة</p>
                        {% endif %}
                    </div>
                </div>

                <!-- الحوادث -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> الحوادث المبلغ عنها</h5>
                    </div>
                    <div class="card-body">
                        {% if incidents %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>الموقع</th>
                                            <th>الحالة</th>
                                            <th>المركبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for incident in incidents %}
                                        <tr>
                                            <td>{{ incident.date_time|date:"Y-m-d H:i" }}</td>
                                            <td>{{ incident.description|truncatewords:10 }}</td>
                                            <td>{{ incident.location }}</td>
                                            <td>{{ incident.get_status_display }}</td>
                                            <td>{{ incident.vehicle.plate_number }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد حوادث مسجلة</p>
                        {% endif %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
