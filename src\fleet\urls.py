from django.urls import path
from . import views

app_name = 'fleet'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),

    # Vehicle URLs
    path('vehicles/', views.vehicle_list, name='vehicle_list'),
    path('vehicles/create/', views.vehicle_create, name='vehicle_create'),
    path('vehicles/<int:pk>/', views.vehicle_detail, name='vehicle_detail'),
    path('vehicles/<int:pk>/edit/', views.vehicle_edit, name='vehicle_edit'),

    # Driver URLs
    path('drivers/', views.driver_list, name='driver_list'),
    path('drivers/create/', views.driver_create, name='driver_create'),
    path('drivers/<int:pk>/', views.driver_detail, name='driver_detail'),
    path('drivers/<int:pk>/edit/', views.driver_edit, name='driver_edit'),

    # Assignment URLs
    path('assignments/', views.vehicle_assignments, name='vehicle_assignments'),
    path('assignments/create/', views.assignment_create, name='assignment_create'),
    path('assignments/<int:pk>/end/', views.assignment_end, name='assignment_end'),

    # Fuel Consumption URLs
    path('fuel-consumption/', views.fuel_consumption_list, name='fuel_consumption_list'),
    path('fuel-consumption/create/', views.fuel_consumption_create, name='fuel_consumption_create'),
    path('fuel-consumption/<int:pk>/', views.fuel_consumption_detail, name='fuel_consumption_detail'),
    path('vehicles/<int:vehicle_pk>/fuel-history/', views.vehicle_fuel_history, name='vehicle_fuel_history'),

    # Odometer Reading URLs
    path('odometer-readings/', views.odometer_reading_list, name='odometer_reading_list'),
    path('odometer-readings/create/', views.odometer_reading_create, name='odometer_reading_create'),
    path('odometer-readings/<int:pk>/', views.odometer_reading_detail, name='odometer_reading_detail'),
    path('odometer-readings/<int:pk>/verify/', views.odometer_reading_verify, name='odometer_reading_verify'),
    path('vehicles/<int:vehicle_pk>/odometer-history/', views.vehicle_odometer_history, name='vehicle_odometer_history'),

    # Fuel Refill URLs
    path('fuel-refills/', views.fuel_refill_list, name='fuel_refill_list'),
    path('fuel-refills/create/', views.fuel_refill_create, name='fuel_refill_create'),
    path('fuel-refills/<int:pk>/', views.fuel_refill_detail, name='fuel_refill_detail'),
    path('fuel-refills/<int:pk>/approve/', views.fuel_refill_approve, name='fuel_refill_approve'),
    path('vehicles/<int:vehicle_pk>/fuel-refill-history/', views.vehicle_fuel_refill_history, name='vehicle_fuel_refill_history'),

    # Daily Route URLs
    path('daily-routes/', views.daily_route_list, name='daily_route_list'),
    path('daily-routes/create/', views.daily_route_create, name='daily_route_create'),
    path('daily-routes/<int:pk>/', views.daily_route_detail, name='daily_route_detail'),
    path('daily-routes/<int:pk>/start/', views.daily_route_start, name='daily_route_start'),
    path('daily-routes/<int:pk>/complete/', views.daily_route_complete, name='daily_route_complete'),
    path('vehicles/<int:vehicle_pk>/daily-routes/', views.vehicle_daily_routes, name='vehicle_daily_routes'),
]