from django.urls import path
from . import views

app_name = 'fleet'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),

    # Vehicle URLs
    path('vehicles/', views.vehicle_list, name='vehicle_list'),
    path('vehicles/create/', views.vehicle_create, name='vehicle_create'),
    path('vehicles/<int:pk>/', views.vehicle_detail, name='vehicle_detail'),
    path('vehicles/<int:pk>/edit/', views.vehicle_edit, name='vehicle_edit'),

    # Driver URLs
    path('drivers/', views.driver_list, name='driver_list'),
    path('drivers/create/', views.driver_create, name='driver_create'),
    path('drivers/<int:pk>/', views.driver_detail, name='driver_detail'),
    path('drivers/<int:pk>/edit/', views.driver_edit, name='driver_edit'),

    # Assignment URLs
    path('assignments/', views.vehicle_assignments, name='vehicle_assignments'),
    path('assignments/create/', views.assignment_create, name='assignment_create'),
    path('assignments/<int:pk>/end/', views.assignment_end, name='assignment_end'),

    # Fuel Consumption URLs
    path('fuel-consumption/', views.fuel_consumption_list, name='fuel_consumption_list'),
    path('fuel-consumption/create/', views.fuel_consumption_create, name='fuel_consumption_create'),
    path('fuel-consumption/<int:pk>/', views.fuel_consumption_detail, name='fuel_consumption_detail'),
    path('vehicles/<int:vehicle_pk>/fuel-history/', views.vehicle_fuel_history, name='vehicle_fuel_history'),
]