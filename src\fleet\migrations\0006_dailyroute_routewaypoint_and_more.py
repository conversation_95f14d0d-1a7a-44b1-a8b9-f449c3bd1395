# Generated by Django 5.2.3 on 2025-06-11 06:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fleet', '0005_fuelrefill'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_date', models.DateField(verbose_name='تاريخ الرحلة')),
                ('route_name', models.CharField(max_length=200, verbose_name='اسم الرحلة')),
                ('route_type', models.CharField(choices=[('delivery', 'توصيل'), ('pickup', 'استلام'), ('service', 'خدمة'), ('transport', 'نقل'), ('maintenance', 'صيانة'), ('emergency', 'طوارئ'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الرحلة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('start_location', models.CharField(max_length=300, verbose_name='نقطة البداية')),
                ('start_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='خط العرض - البداية')),
                ('start_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='خط الطول - البداية')),
                ('end_location', models.CharField(max_length=300, verbose_name='نقطة النهاية')),
                ('end_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='خط العرض - النهاية')),
                ('end_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='خط الطول - النهاية')),
                ('planned_start_time', models.TimeField(verbose_name='الوقت المخطط للبداية')),
                ('planned_end_time', models.TimeField(verbose_name='الوقت المخطط للنهاية')),
                ('actual_start_time', models.TimeField(blank=True, null=True, verbose_name='الوقت الفعلي للبداية')),
                ('actual_end_time', models.TimeField(blank=True, null=True, verbose_name='الوقت الفعلي للنهاية')),
                ('start_odometer', models.PositiveIntegerField(blank=True, null=True, verbose_name='قراءة العداد عند البداية')),
                ('end_odometer', models.PositiveIntegerField(blank=True, null=True, verbose_name='قراءة العداد عند النهاية')),
                ('estimated_distance', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='المسافة المقدرة (كم)')),
                ('estimated_duration', models.DurationField(blank=True, null=True, verbose_name='المدة المقدرة')),
                ('status', models.CharField(choices=[('planned', 'مخطط'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('delayed', 'متأخر')], default='planned', max_length=20, verbose_name='حالة الرحلة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الرحلة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('client_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم العميل/الجهة')),
                ('client_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف العميل')),
                ('client_address', models.TextField(blank=True, null=True, verbose_name='عنوان العميل')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة المقدرة')),
                ('actual_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة الفعلية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_routes', to='fleet.userprofile', verbose_name='أنشئ بواسطة')),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_routes', to='fleet.driver', verbose_name='السائق')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_routes', to='fleet.vehicle', verbose_name='المركبة')),
            ],
            options={
                'verbose_name': 'خط سير يومي',
                'verbose_name_plural': 'خطوط السير اليومية',
                'ordering': ['-route_date', '-planned_start_time'],
            },
        ),
        migrations.CreateModel(
            name='RouteWaypoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم النقطة')),
                ('waypoint_type', models.CharField(choices=[('pickup', 'نقطة استلام'), ('delivery', 'نقطة توصيل'), ('stop', 'نقطة توقف'), ('fuel', 'محطة وقود'), ('rest', 'استراحة'), ('checkpoint', 'نقطة تفتيش')], max_length=20, verbose_name='نوع النقطة')),
                ('order', models.PositiveIntegerField(verbose_name='ترتيب النقطة')),
                ('address', models.CharField(max_length=300, verbose_name='العنوان')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='خط العرض')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='خط الطول')),
                ('planned_arrival_time', models.TimeField(verbose_name='الوقت المخطط للوصول')),
                ('actual_arrival_time', models.TimeField(blank=True, null=True, verbose_name='الوقت الفعلي للوصول')),
                ('planned_departure_time', models.TimeField(blank=True, null=True, verbose_name='الوقت المخطط للمغادرة')),
                ('actual_departure_time', models.TimeField(blank=True, null=True, verbose_name='الوقت الفعلي للمغادرة')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('special_instructions', models.TextField(blank=True, null=True, verbose_name='تعليمات خاصة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('reached', 'تم الوصول'), ('completed', 'مكتمل'), ('skipped', 'تم التجاوز')], default='pending', max_length=20, verbose_name='حالة النقطة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='waypoints', to='fleet.dailyroute', verbose_name='الرحلة')),
            ],
            options={
                'verbose_name': 'نقطة وسطية',
                'verbose_name_plural': 'النقاط الوسطية',
                'ordering': ['route', 'order'],
            },
        ),
        migrations.AddIndex(
            model_name='dailyroute',
            index=models.Index(fields=['vehicle', '-route_date'], name='fleet_daily_vehicle_698cae_idx'),
        ),
        migrations.AddIndex(
            model_name='dailyroute',
            index=models.Index(fields=['driver', '-route_date'], name='fleet_daily_driver__42d953_idx'),
        ),
        migrations.AddIndex(
            model_name='dailyroute',
            index=models.Index(fields=['route_date', 'status'], name='fleet_daily_route_d_a2d590_idx'),
        ),
        migrations.AddIndex(
            model_name='dailyroute',
            index=models.Index(fields=['status'], name='fleet_daily_status_625a9f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='dailyroute',
            unique_together={('vehicle', 'route_date', 'planned_start_time')},
        ),
        migrations.AlterUniqueTogether(
            name='routewaypoint',
            unique_together={('route', 'order')},
        ),
    ]
