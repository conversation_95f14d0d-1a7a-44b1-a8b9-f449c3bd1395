from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class Company(models.Model):
    name = models.CharField(max_length=100)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    activity = models.CharField(max_length=100)
    location = models.CharField(max_length=255, blank=True, null=True)  # For geographic coordinates
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = "Companies"

class UserProfile(models.Model):
    ROLE_CHOICES = [
        ('manager', 'Manager'),
        ('supervisor', 'Supervisor'),
        ('monitor', 'Monitor'),
        ('driver', 'Driver'),
        ('support', 'Technical Support'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='employees')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    phone = models.CharField(max_length=20, blank=True, null=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.role} at {self.company.name}"

class Driver(models.Model):
    """نموذج السائقين المستقل"""
    DRIVER_STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('suspended', 'موقوف'),
        ('on_leave', 'في إجازة'),
    ]

    LICENSE_TYPE_CHOICES = [
        ('private', 'رخصة خاصة'),
        ('public', 'رخصة عامة'),
        ('heavy', 'رخصة ثقيلة'),
        ('motorcycle', 'رخصة دراجة نارية'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='drivers')
    user_profile = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='driver_info', null=True, blank=True)

    # معلومات شخصية
    first_name = models.CharField(max_length=50, verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=50, verbose_name='اسم العائلة')
    national_id = models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(verbose_name='العنوان')
    birth_date = models.DateField(verbose_name='تاريخ الميلاد')

    # معلومات الرخصة
    license_number = models.CharField(max_length=30, unique=True, verbose_name='رقم الرخصة')
    license_type = models.CharField(max_length=20, choices=LICENSE_TYPE_CHOICES, verbose_name='نوع الرخصة')
    license_issue_date = models.DateField(verbose_name='تاريخ إصدار الرخصة')
    license_expiry_date = models.DateField(verbose_name='تاريخ انتهاء الرخصة')

    # معلومات العمل
    hire_date = models.DateField(verbose_name='تاريخ التوظيف')
    salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='الراتب')
    status = models.CharField(max_length=20, choices=DRIVER_STATUS_CHOICES, default='active', verbose_name='الحالة')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'سائق'
        verbose_name_plural = 'السائقين'
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.license_number}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def is_license_expired(self):
        from django.utils import timezone
        return self.license_expiry_date < timezone.now().date()


class Vehicle(models.Model):
    VEHICLE_TYPE_CHOICES = [
        ('goods', 'Goods Transport'),
        ('people', 'People Transport'),
        ('special', 'Special Vehicle'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('maintenance', 'Under Maintenance'),
        ('inactive', 'Inactive'),
    ]

    FUEL_TYPE_CHOICES = [
        ('gasoline', 'بنزين'),
        ('diesel', 'ديزل'),
        ('hybrid', 'هجين'),
        ('electric', 'كهربائي'),
        ('lpg', 'غاز البترول المسال'),
        ('cng', 'غاز طبيعي مضغوط'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='vehicles')
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPE_CHOICES)
    plate_number = models.CharField(max_length=20, unique=True, verbose_name='رقم اللوحة')
    model = models.CharField(max_length=100, verbose_name='الموديل')

    # معلومات المركبة التقنية
    chassis_number = models.CharField(max_length=50, unique=True, verbose_name='رقم الشاسية')
    engine_number = models.CharField(max_length=50, unique=True, verbose_name='رقم الموتور')
    fuel_type = models.CharField(max_length=20, choices=FUEL_TYPE_CHOICES, verbose_name='نوع الوقود')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    # إزالة الحقل القديم driver وسيتم استبداله بجدول VehicleDriverAssignment
    capacity = models.CharField(max_length=50, blank=True, null=True)
    last_maintenance = models.DateField(null=True, blank=True)
    insurance_expiry = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.plate_number} - {self.model}"

    @property
    def current_driver(self):
        """الحصول على السائق الحالي للمركبة"""
        current_assignment = self.driver_assignments.filter(end_date__isnull=True).first()
        return current_assignment.driver if current_assignment else None


class VehicleDriverAssignment(models.Model):
    """نموذج ربط السائقين بالمركبات مع تتبع التواريخ"""
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='driver_assignments', verbose_name='المركبة')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='vehicle_assignments', verbose_name='السائق')

    # تواريخ الاستلام والتسليم
    start_date = models.DateTimeField(verbose_name='تاريخ بدء الاستلام')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ نهاية الاستلام')

    # بيانات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name='البيان/الملاحظات')
    assigned_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_vehicles', verbose_name='تم التعيين بواسطة')

    # حالة التعيين
    ASSIGNMENT_STATUS_CHOICES = [
        ('active', 'نشط'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]
    status = models.CharField(max_length=20, choices=ASSIGNMENT_STATUS_CHOICES, default='active', verbose_name='حالة التعيين')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'تعيين سائق لمركبة'
        verbose_name_plural = 'تعيينات السائقين للمركبات'
        ordering = ['-start_date']

        # التأكد من عدم وجود أكثر من تعيين نشط لنفس المركبة
        constraints = [
            models.UniqueConstraint(
                fields=['vehicle'],
                condition=models.Q(end_date__isnull=True),
                name='unique_active_vehicle_assignment'
            )
        ]

    def __str__(self):
        status_text = "نشط" if self.end_date is None else "مكتمل"
        return f"{self.driver.full_name} - {self.vehicle.plate_number} ({status_text})"

    def save(self, *args, **kwargs):
        # إذا كان هذا تعيين جديد نشط، قم بإنهاء التعيين السابق
        if not self.pk and self.end_date is None:
            VehicleDriverAssignment.objects.filter(
                vehicle=self.vehicle,
                end_date__isnull=True
            ).update(end_date=self.start_date, status='completed')

        super().save(*args, **kwargs)

    @property
    def duration(self):
        """حساب مدة التعيين"""
        if self.end_date:
            return self.end_date - self.start_date
        else:
            from django.utils import timezone
            return timezone.now() - self.start_date

    @property
    def is_active(self):
        """التحقق من كون التعيين نشط"""
        return self.end_date is None and self.status == 'active'

class Trip(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('cancelled', 'Cancelled'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='trips')
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='trips')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='trips')
    start_location = models.CharField(max_length=255)
    end_location = models.CharField(max_length=255)
    start_time = models.DateTimeField()
    estimated_end_time = models.DateTimeField()
    actual_end_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Trip {self.id} - {self.vehicle.plate_number}"

class Maintenance(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='maintenance_records')
    description = models.TextField()
    scheduled_date = models.DateField()
    completion_date = models.DateField(null=True, blank=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Maintenance for {self.vehicle.plate_number} on {self.scheduled_date}"

class Incident(models.Model):
    STATUS_CHOICES = [
        ('reported', 'Reported'),
        ('investigating', 'Investigating'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='incidents')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='reported_incidents')
    description = models.TextField()
    location = models.CharField(max_length=255)
    date_time = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='reported')
    resolution = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Incident {self.id} - {self.vehicle.plate_number}"

class IncidentImage(models.Model):
    incident = models.ForeignKey(Incident, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='incidents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Image for Incident {self.incident.id}"

class LocationLog(models.Model):
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='location_logs')
    latitude = models.FloatField()
    longitude = models.FloatField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Location of {self.vehicle.plate_number} at {self.timestamp}"


# Signal to create UserProfile when User is created
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        # Create a default company if none exists
        default_company, created = Company.objects.get_or_create(
            name="شركة افتراضية",
            defaults={
                'address': 'عنوان افتراضي',
                'phone': '************',
                'activity': 'نشاط افتراضي'
            }
        )

        # Create UserProfile with default values
        UserProfile.objects.create(
            user=instance,
            company=default_company,
            role='manager'  # Default role
        )
