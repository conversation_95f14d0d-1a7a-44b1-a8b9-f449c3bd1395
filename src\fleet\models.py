from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class Company(models.Model):
    name = models.CharField(max_length=100)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    activity = models.CharField(max_length=100)
    location = models.CharField(max_length=255, blank=True, null=True)  # For geographic coordinates
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name_plural = "Companies"

class UserProfile(models.Model):
    ROLE_CHOICES = [
        ('manager', 'Manager'),
        ('supervisor', 'Supervisor'),
        ('monitor', 'Monitor'),
        ('driver', 'Driver'),
        ('support', 'Technical Support'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='employees')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    phone = models.CharField(max_length=20, blank=True, null=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.role} at {self.company.name}"

class Driver(models.Model):
    """نموذج السائقين المستقل"""
    DRIVER_STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('suspended', 'موقوف'),
        ('on_leave', 'في إجازة'),
    ]

    LICENSE_TYPE_CHOICES = [
        ('private', 'رخصة خاصة'),
        ('public', 'رخصة عامة'),
        ('heavy', 'رخصة ثقيلة'),
        ('motorcycle', 'رخصة دراجة نارية'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='drivers')
    user_profile = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='driver_info', null=True, blank=True)

    # معلومات شخصية
    first_name = models.CharField(max_length=50, verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=50, verbose_name='اسم العائلة')
    national_id = models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(verbose_name='العنوان')
    birth_date = models.DateField(verbose_name='تاريخ الميلاد')

    # معلومات الرخصة
    license_number = models.CharField(max_length=30, unique=True, verbose_name='رقم الرخصة')
    license_type = models.CharField(max_length=20, choices=LICENSE_TYPE_CHOICES, verbose_name='نوع الرخصة')
    license_issue_date = models.DateField(verbose_name='تاريخ إصدار الرخصة')
    license_expiry_date = models.DateField(verbose_name='تاريخ انتهاء الرخصة')

    # معلومات العمل
    hire_date = models.DateField(verbose_name='تاريخ التوظيف')
    salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='الراتب')
    status = models.CharField(max_length=20, choices=DRIVER_STATUS_CHOICES, default='active', verbose_name='الحالة')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'سائق'
        verbose_name_plural = 'السائقين'
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.license_number}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def is_license_expired(self):
        from django.utils import timezone
        return self.license_expiry_date < timezone.now().date()


class Vehicle(models.Model):
    VEHICLE_TYPE_CHOICES = [
        ('goods', 'Goods Transport'),
        ('people', 'People Transport'),
        ('special', 'Special Vehicle'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('maintenance', 'Under Maintenance'),
        ('inactive', 'Inactive'),
    ]

    FUEL_TYPE_CHOICES = [
        ('gasoline', 'بنزين'),
        ('diesel', 'ديزل'),
        ('hybrid', 'هجين'),
        ('electric', 'كهربائي'),
        ('lpg', 'غاز البترول المسال'),
        ('cng', 'غاز طبيعي مضغوط'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='vehicles')
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPE_CHOICES)
    plate_number = models.CharField(max_length=20, unique=True, verbose_name='رقم اللوحة')
    model = models.CharField(max_length=100, verbose_name='الموديل')

    # معلومات المركبة التقنية
    chassis_number = models.CharField(max_length=50, unique=True, null=True, blank=True, verbose_name='رقم الشاسية')
    engine_number = models.CharField(max_length=50, unique=True, null=True, blank=True, verbose_name='رقم الموتور')
    fuel_type = models.CharField(max_length=20, choices=FUEL_TYPE_CHOICES, default='gasoline', verbose_name='نوع الوقود')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    # إزالة الحقل القديم driver وسيتم استبداله بجدول VehicleDriverAssignment
    capacity = models.CharField(max_length=50, blank=True, null=True)
    last_maintenance = models.DateField(null=True, blank=True)
    insurance_expiry = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.plate_number} - {self.model}"

    @property
    def current_driver(self):
        """الحصول على السائق الحالي للمركبة"""
        current_assignment = self.driver_assignments.filter(end_date__isnull=True).first()
        return current_assignment.driver if current_assignment else None

    @property
    def latest_fuel_consumption(self):
        """الحصول على آخر قياس استهلاك وقود"""
        return self.fuel_consumptions.first()

    @property
    def average_fuel_consumption(self):
        """حساب متوسط استهلاك الوقود"""
        consumptions = self.fuel_consumptions.all()
        if consumptions:
            total = sum(c.consumption_rate for c in consumptions)
            return round(total / len(consumptions), 2)
        return None

    @property
    def latest_odometer_reading(self):
        """الحصول على آخر قراءة عداد"""
        return self.odometer_readings.filter(status='verified').first()

    @property
    def current_odometer_reading(self):
        """الحصول على قراءة العداد الحالية"""
        latest = self.latest_odometer_reading
        return latest.odometer_reading if latest else None

    @property
    def total_distance_this_month(self):
        """حساب إجمالي المسافة لهذا الشهر"""
        from django.utils import timezone
        current_month = timezone.now().date().replace(day=1)

        readings = self.odometer_readings.filter(
            reading_date__gte=current_month,
            status='verified',
            daily_distance__isnull=False
        )

        total = sum(reading.daily_distance for reading in readings)
        return total


class VehicleDriverAssignment(models.Model):
    """نموذج ربط السائقين بالمركبات مع تتبع التواريخ"""
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='driver_assignments', verbose_name='المركبة')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='vehicle_assignments', verbose_name='السائق')

    # تواريخ الاستلام والتسليم
    start_date = models.DateTimeField(verbose_name='تاريخ بدء الاستلام')
    end_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ نهاية الاستلام')

    # بيانات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name='البيان/الملاحظات')
    assigned_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_vehicles', verbose_name='تم التعيين بواسطة')

    # حالة التعيين
    ASSIGNMENT_STATUS_CHOICES = [
        ('active', 'نشط'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]
    status = models.CharField(max_length=20, choices=ASSIGNMENT_STATUS_CHOICES, default='active', verbose_name='حالة التعيين')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'تعيين سائق لمركبة'
        verbose_name_plural = 'تعيينات السائقين للمركبات'
        ordering = ['-start_date']

        # التأكد من عدم وجود أكثر من تعيين نشط لنفس المركبة
        constraints = [
            models.UniqueConstraint(
                fields=['vehicle'],
                condition=models.Q(end_date__isnull=True),
                name='unique_active_vehicle_assignment'
            )
        ]

    def __str__(self):
        status_text = "نشط" if self.end_date is None else "مكتمل"
        return f"{self.driver.full_name} - {self.vehicle.plate_number} ({status_text})"

    def save(self, *args, **kwargs):
        # إذا كان هذا تعيين جديد نشط، قم بإنهاء التعيين السابق
        if not self.pk and self.end_date is None:
            VehicleDriverAssignment.objects.filter(
                vehicle=self.vehicle,
                end_date__isnull=True
            ).update(end_date=self.start_date, status='completed')

        super().save(*args, **kwargs)

    @property
    def duration(self):
        """حساب مدة التعيين"""
        if self.end_date:
            return self.end_date - self.start_date
        else:
            from django.utils import timezone
            return timezone.now() - self.start_date

    @property
    def is_active(self):
        """التحقق من كون التعيين نشط"""
        return self.end_date is None and self.status == 'active'


class FuelConsumption(models.Model):
    """نموذج تتبع استهلاك الوقود للمركبات"""

    MEASUREMENT_TYPE_CHOICES = [
        ('manual', 'قياس يدوي'),
        ('automatic', 'قياس تلقائي'),
        ('estimated', 'تقدير'),
    ]

    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='fuel_consumptions', verbose_name='المركبة')

    # بيانات الاستهلاك
    consumption_rate = models.DecimalField(max_digits=6, decimal_places=2, verbose_name='معدل الاستهلاك (لتر/100كم)')
    distance_traveled = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='المسافة المقطوعة (كم)')
    fuel_amount = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='كمية الوقود (لتر)')

    # تواريخ القياس
    measurement_date = models.DateTimeField(verbose_name='تاريخ المعايرة')
    period_start = models.DateField(null=True, blank=True, verbose_name='بداية فترة القياس')
    period_end = models.DateField(null=True, blank=True, verbose_name='نهاية فترة القياس')

    # معلومات إضافية
    measurement_type = models.CharField(max_length=20, choices=MEASUREMENT_TYPE_CHOICES, default='manual', verbose_name='نوع القياس')
    odometer_reading = models.IntegerField(null=True, blank=True, verbose_name='قراءة العداد (كم)')
    notes = models.TextField(blank=True, null=True, verbose_name='البيان/الملاحظات')

    # معلومات الظروف
    weather_condition = models.CharField(max_length=50, blank=True, null=True, verbose_name='حالة الطقس')
    road_type = models.CharField(max_length=50, blank=True, null=True, verbose_name='نوع الطريق')
    load_condition = models.CharField(max_length=50, blank=True, null=True, verbose_name='حالة التحميل')

    # من قام بالقياس
    recorded_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='fuel_measurements', verbose_name='تم التسجيل بواسطة')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'قياس استهلاك الوقود'
        verbose_name_plural = 'قياسات استهلاك الوقود'
        ordering = ['-measurement_date']

        # فهرسة للبحث السريع
        indexes = [
            models.Index(fields=['vehicle', '-measurement_date']),
            models.Index(fields=['measurement_date']),
        ]

    def __str__(self):
        return f"{self.vehicle.plate_number} - {self.consumption_rate} لتر/100كم ({self.measurement_date.date()})"

    @property
    def efficiency_rating(self):
        """تقييم كفاءة الاستهلاك"""
        if self.consumption_rate <= 6:
            return 'ممتاز'
        elif self.consumption_rate <= 8:
            return 'جيد'
        elif self.consumption_rate <= 10:
            return 'متوسط'
        elif self.consumption_rate <= 12:
            return 'ضعيف'
        else:
            return 'سيء جداً'

    @property
    def efficiency_color(self):
        """لون تقييم الكفاءة للعرض"""
        rating = self.efficiency_rating
        colors = {
            'ممتاز': 'success',
            'جيد': 'info',
            'متوسط': 'warning',
            'ضعيف': 'danger',
            'سيء جداً': 'dark'
        }
        return colors.get(rating, 'secondary')

    def save(self, *args, **kwargs):
        # حساب معدل الاستهلاك تلقائياً إذا لم يتم إدخاله
        if not self.consumption_rate and self.fuel_amount and self.distance_traveled:
            if self.distance_traveled > 0:
                self.consumption_rate = (self.fuel_amount / self.distance_traveled) * 100

        super().save(*args, **kwargs)

class Trip(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('delayed', 'Delayed'),
        ('cancelled', 'Cancelled'),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='trips')
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='trips')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='trips')
    start_location = models.CharField(max_length=255)
    end_location = models.CharField(max_length=255)
    start_time = models.DateTimeField()
    estimated_end_time = models.DateTimeField()
    actual_end_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Trip {self.id} - {self.vehicle.plate_number}"

class Maintenance(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='maintenance_records')
    description = models.TextField()
    scheduled_date = models.DateField()
    completion_date = models.DateField(null=True, blank=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Maintenance for {self.vehicle.plate_number} on {self.scheduled_date}"

class Incident(models.Model):
    STATUS_CHOICES = [
        ('reported', 'Reported'),
        ('investigating', 'Investigating'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='incidents')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='reported_incidents')
    description = models.TextField()
    location = models.CharField(max_length=255)
    date_time = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='reported')
    resolution = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Incident {self.id} - {self.vehicle.plate_number}"

class IncidentImage(models.Model):
    incident = models.ForeignKey(Incident, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='incidents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Image for Incident {self.incident.id}"

class LocationLog(models.Model):
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='location_logs')
    latitude = models.FloatField()
    longitude = models.FloatField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Location of {self.vehicle.plate_number} at {self.timestamp}"


class DailyOdometerReading(models.Model):
    """نموذج تسجيل قراءة عداد الكيلومتر اليومية"""

    READING_STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('verified', 'تم التحقق'),
        ('rejected', 'مرفوض'),
    ]

    SHIFT_CHOICES = [
        ('morning', 'الصباح'),
        ('evening', 'المساء'),
        ('night', 'الليل'),
        ('full_day', 'يوم كامل'),
    ]

    # معلومات أساسية
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='odometer_readings', verbose_name='المركبة')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE, related_name='odometer_readings', verbose_name='السائق')

    # بيانات القراءة
    reading_date = models.DateField(verbose_name='تاريخ القراءة')
    odometer_reading = models.PositiveIntegerField(verbose_name='قراءة العداد (كم)')
    previous_reading = models.PositiveIntegerField(null=True, blank=True, verbose_name='القراءة السابقة (كم)')
    daily_distance = models.PositiveIntegerField(null=True, blank=True, verbose_name='المسافة اليومية (كم)')

    # معلومات الوردية
    shift = models.CharField(max_length=20, choices=SHIFT_CHOICES, default='full_day', verbose_name='الوردية')
    start_time = models.TimeField(null=True, blank=True, verbose_name='وقت البداية')
    end_time = models.TimeField(null=True, blank=True, verbose_name='وقت النهاية')

    # صورة العداد
    odometer_image = models.ImageField(
        upload_to='odometer_readings/%Y/%m/%d/',
        null=True,
        blank=True,
        verbose_name='صورة العداد'
    )

    # معلومات إضافية
    fuel_level = models.CharField(max_length=20, null=True, blank=True, verbose_name='مستوى الوقود')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    location = models.CharField(max_length=200, null=True, blank=True, verbose_name='الموقع')

    # حالة التسجيل
    status = models.CharField(max_length=20, choices=READING_STATUS_CHOICES, default='pending', verbose_name='حالة التسجيل')
    verified_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_readings',
        verbose_name='تم التحقق بواسطة'
    )
    verification_date = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ التحقق')
    rejection_reason = models.TextField(blank=True, null=True, verbose_name='سبب الرفض')

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'قراءة عداد يومية'
        verbose_name_plural = 'قراءات العداد اليومية'
        ordering = ['-reading_date', '-created_at']

        # منع تكرار القراءة لنفس المركبة والسائق في نفس اليوم والوردية
        unique_together = ['vehicle', 'driver', 'reading_date', 'shift']

        # فهرسة للبحث السريع
        indexes = [
            models.Index(fields=['vehicle', '-reading_date']),
            models.Index(fields=['driver', '-reading_date']),
            models.Index(fields=['reading_date']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.vehicle.plate_number} - {self.driver.full_name} - {self.reading_date} ({self.odometer_reading} كم)"

    def save(self, *args, **kwargs):
        # حساب المسافة اليومية تلقائياً
        if not self.daily_distance and self.previous_reading:
            if self.odometer_reading > self.previous_reading:
                self.daily_distance = self.odometer_reading - self.previous_reading

        # الحصول على القراءة السابقة تلقائياً إذا لم تكن محددة
        if not self.previous_reading:
            previous_reading_obj = DailyOdometerReading.objects.filter(
                vehicle=self.vehicle,
                reading_date__lt=self.reading_date,
                status='verified'
            ).order_by('-reading_date').first()

            if previous_reading_obj:
                self.previous_reading = previous_reading_obj.odometer_reading
                if not self.daily_distance:
                    self.daily_distance = self.odometer_reading - self.previous_reading

        super().save(*args, **kwargs)

    @property
    def is_verified(self):
        """التحقق من كون القراءة محققة"""
        return self.status == 'verified'

    @property
    def is_pending(self):
        """التحقق من كون القراءة في الانتظار"""
        return self.status == 'pending'

    @property
    def is_rejected(self):
        """التحقق من كون القراءة مرفوضة"""
        return self.status == 'rejected'

    @property
    def distance_status(self):
        """تقييم المسافة اليومية"""
        if not self.daily_distance:
            return 'غير محدد'

        if self.daily_distance == 0:
            return 'لم تتحرك'
        elif self.daily_distance <= 50:
            return 'قصيرة'
        elif self.daily_distance <= 200:
            return 'متوسطة'
        elif self.daily_distance <= 500:
            return 'طويلة'
        else:
            return 'طويلة جداً'

    @property
    def distance_color(self):
        """لون تقييم المسافة للعرض"""
        status = self.distance_status
        colors = {
            'لم تتحرك': 'secondary',
            'قصيرة': 'success',
            'متوسطة': 'info',
            'طويلة': 'warning',
            'طويلة جداً': 'danger',
            'غير محدد': 'light'
        }
        return colors.get(status, 'secondary')

    def clean(self):
        from django.core.exceptions import ValidationError

        # التحقق من صحة قراءة العداد
        if self.previous_reading and self.odometer_reading < self.previous_reading:
            raise ValidationError('قراءة العداد لا يمكن أن تكون أقل من القراءة السابقة')

        # التحقق من منطقية المسافة اليومية
        if self.daily_distance and self.daily_distance > 1000:
            raise ValidationError('المسافة اليومية تبدو غير منطقية (أكثر من 1000 كم)')

        # التحقق من تاريخ القراءة
        from django.utils import timezone
        if self.reading_date > timezone.now().date():
            raise ValidationError('لا يمكن تسجيل قراءة لتاريخ مستقبلي')


# Signal to create UserProfile when User is created
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        # Create a default company if none exists
        default_company, created = Company.objects.get_or_create(
            name="شركة افتراضية",
            defaults={
                'address': 'عنوان افتراضي',
                'phone': '************',
                'activity': 'نشاط افتراضي'
            }
        )

        # Create UserProfile with default values
        UserProfile.objects.create(
            user=instance,
            company=default_company,
            role='manager'  # Default role
        )
