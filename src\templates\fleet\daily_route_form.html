<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:daily_route_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- معلومات أساسية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle.id_for_label }}" class="form-label">{{ form.vehicle.label }} <span class="text-danger">*</span></label>
                                            {{ form.vehicle }}
                                            {% if form.vehicle.errors %}
                                                <div class="text-danger">{{ form.vehicle.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.driver.id_for_label }}" class="form-label">{{ form.driver.label }} <span class="text-danger">*</span></label>
                                            {{ form.driver }}
                                            {% if form.driver.errors %}
                                                <div class="text-danger">{{ form.driver.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.route_date.id_for_label }}" class="form-label">{{ form.route_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.route_date }}
                                            {% if form.route_date.errors %}
                                                <div class="text-danger">{{ form.route_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.route_name.id_for_label }}" class="form-label">{{ form.route_name.label }} <span class="text-danger">*</span></label>
                                            {{ form.route_name }}
                                            {% if form.route_name.errors %}
                                                <div class="text-danger">{{ form.route_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.route_type.id_for_label }}" class="form-label">{{ form.route_type.label }} <span class="text-danger">*</span></label>
                                            {{ form.route_type }}
                                            {% if form.route_type.errors %}
                                                <div class="text-danger">{{ form.route_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                                            {{ form.priority }}
                                            {% if form.priority.errors %}
                                                <div class="text-danger">{{ form.priority.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- نقاط البداية والنهاية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-map-marker-alt"></i> نقاط البداية والنهاية</h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-primary">نقطة البداية</h6>
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="{{ form.start_location.id_for_label }}" class="form-label">{{ form.start_location.label }} <span class="text-danger">*</span></label>
                                            {{ form.start_location }}
                                            {% if form.start_location.errors %}
                                                <div class="text-danger">{{ form.start_location.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.start_latitude.id_for_label }}" class="form-label">{{ form.start_latitude.label }}</label>
                                            {{ form.start_latitude }}
                                            {% if form.start_latitude.errors %}
                                                <div class="text-danger">{{ form.start_latitude.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.start_longitude.id_for_label }}" class="form-label">{{ form.start_longitude.label }}</label>
                                            {{ form.start_longitude }}
                                            {% if form.start_longitude.errors %}
                                                <div class="text-danger">{{ form.start_longitude.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <hr>

                                    <h6 class="text-success">نقطة النهاية</h6>
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="{{ form.end_location.id_for_label }}" class="form-label">{{ form.end_location.label }} <span class="text-danger">*</span></label>
                                            {{ form.end_location }}
                                            {% if form.end_location.errors %}
                                                <div class="text-danger">{{ form.end_location.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.end_latitude.id_for_label }}" class="form-label">{{ form.end_latitude.label }}</label>
                                            {{ form.end_latitude }}
                                            {% if form.end_latitude.errors %}
                                                <div class="text-danger">{{ form.end_latitude.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.end_longitude.id_for_label }}" class="form-label">{{ form.end_longitude.label }}</label>
                                            {{ form.end_longitude }}
                                            {% if form.end_longitude.errors %}
                                                <div class="text-danger">{{ form.end_longitude.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الأوقات والتقديرات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock"></i> الأوقات والتقديرات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.planned_start_time.id_for_label }}" class="form-label">{{ form.planned_start_time.label }} <span class="text-danger">*</span></label>
                                            {{ form.planned_start_time }}
                                            {% if form.planned_start_time.errors %}
                                                <div class="text-danger">{{ form.planned_start_time.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.planned_end_time.id_for_label }}" class="form-label">{{ form.planned_end_time.label }} <span class="text-danger">*</span></label>
                                            {{ form.planned_end_time }}
                                            {% if form.planned_end_time.errors %}
                                                <div class="text-danger">{{ form.planned_end_time.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.estimated_distance.id_for_label }}" class="form-label">{{ form.estimated_distance.label }}</label>
                                            {{ form.estimated_distance }}
                                            {% if form.estimated_distance.errors %}
                                                <div class="text-danger">{{ form.estimated_distance.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.estimated_duration.id_for_label }}" class="form-label">{{ form.estimated_duration.label }}</label>
                                            {{ form.estimated_duration }}
                                            {% if form.estimated_duration.errors %}
                                                <div class="text-danger">{{ form.estimated_duration.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.estimated_cost.id_for_label }}" class="form-label">{{ form.estimated_cost.label }}</label>
                                            {{ form.estimated_cost }}
                                            {% if form.estimated_cost.errors %}
                                                <div class="text-danger">{{ form.estimated_cost.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العميل -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-user"></i> معلومات العميل/الجهة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.client_name.id_for_label }}" class="form-label">{{ form.client_name.label }}</label>
                                            {{ form.client_name }}
                                            {% if form.client_name.errors %}
                                                <div class="text-danger">{{ form.client_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.client_phone.id_for_label }}" class="form-label">{{ form.client_phone.label }}</label>
                                            {{ form.client_phone }}
                                            {% if form.client_phone.errors %}
                                                <div class="text-danger">{{ form.client_phone.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.client_address.id_for_label }}" class="form-label">{{ form.client_address.label }}</label>
                                        {{ form.client_address }}
                                        {% if form.client_address.errors %}
                                            <div class="text-danger">{{ form.client_address.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- الوصف والملاحظات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-sticky-note"></i> الوصف والملاحظات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                        {{ form.description }}
                                        {% if form.description.errors %}
                                            <div class="text-danger">{{ form.description.errors }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- عرض أخطاء النموذج العامة -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'fleet:daily_route_list' %}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ submit_text }}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> إرشادات التخطيط</h6>
                                </div>
                                <div class="card-body">
                                    <h6>خطوات التخطيط:</h6>
                                    <ol class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> 1. اختر المركبة والسائق</li>
                                        <li><i class="fas fa-check text-success"></i> 2. حدد تاريخ الرحلة</li>
                                        <li><i class="fas fa-check text-success"></i> 3. أدخل اسم الرحلة ونوعها</li>
                                        <li><i class="fas fa-check text-success"></i> 4. حدد نقاط البداية والنهاية</li>
                                        <li><i class="fas fa-check text-success"></i> 5. اختر الأوقات المناسبة</li>
                                        <li><i class="fas fa-check text-success"></i> 6. قدر المسافة والتكلفة</li>
                                        <li><i class="fas fa-check text-success"></i> 7. أضف معلومات العميل</li>
                                    </ol>

                                    <h6 class="mt-3">نصائح مهمة:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-lightbulb text-warning"></i> تحقق من توفر المركبة</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> راعي أوقات الراحة</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> أضف هامش زمني للطوارئ</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> تأكد من دقة العناوين</li>
                                    </ul>

                                    <h6 class="mt-3">أنواع الرحلات:</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-primary">توصيل</span> نقل البضائع للعملاء</li>
                                        <li><span class="badge bg-success">استلام</span> جمع البضائع</li>
                                        <li><span class="badge bg-info">خدمة</span> تقديم خدمات</li>
                                        <li><span class="badge bg-warning">نقل</span> نقل الأشخاص</li>
                                        <li><span class="badge bg-secondary">صيانة</span> أعمال الصيانة</li>
                                        <li><span class="badge bg-danger">طوارئ</span> حالات عاجلة</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-map"></i> نصائح الإحداثيات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> استخدم خرائط جوجل للدقة</li>
                                        <li><i class="fas fa-check text-success"></i> تأكد من صحة الإحداثيات</li>
                                        <li><i class="fas fa-check text-success"></i> اختبر الموقع قبل الحفظ</li>
                                        <li><i class="fas fa-check text-success"></i> أضف معالم مميزة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
