<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطوط سير المركبة - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">خطوط سير المركبة: {{ vehicle.plate_number }}</h1>
                    <div>
                        <a href="{% url 'fleet:vehicle_detail' vehicle.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة لتفاصيل المركبة
                        </a>
                        <a href="{% url 'fleet:daily_route_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إنشاء خط سير جديد
                        </a>
                    </div>
                </div>

                <!-- معلومات المركبة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-truck"></i> معلومات المركبة</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>رقم اللوحة:</strong> {{ vehicle.plate_number }}</p>
                                <p><strong>الموديل:</strong> {{ vehicle.model }}</p>
                                <p><strong>النوع:</strong> {{ vehicle.get_vehicle_type_display }}</p>
                                <p><strong>الحالة:</strong> 
                                    {% if vehicle.status == 'active' %}
                                        <span class="badge bg-success">نشط</span>
                                    {% elif vehicle.status == 'maintenance' %}
                                        <span class="badge bg-warning">تحت الصيانة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-bar"></i> إحصائيات خطوط السير</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h5 class="text-primary">{{ total_routes }}</h5>
                                        <p class="text-muted">إجمالي الرحلات</p>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success">{{ completion_rate }}%</h5>
                                        <p class="text-muted">معدل الإنجاز</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <h5 class="text-info">{{ total_distance }}</h5>
                                        <p class="text-muted">إجمالي المسافة (كم)</p>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-warning">{{ total_cost }}</h5>
                                        <p class="text-muted">إجمالي التكلفة (ريال)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- متوسطات الأداء -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-analytics"></i> متوسطات الأداء</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-primary">{{ avg_distance }}</h5>
                                            <p class="text-muted">متوسط المسافة (كم)</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-success">{{ avg_cost }}</h5>
                                            <p class="text-muted">متوسط التكلفة (ريال)</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-info">{{ total_routes }}</h5>
                                            <p class="text-muted">عدد الرحلات</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-warning">{{ completion_rate }}%</h5>
                                            <p class="text-muted">معدل النجاح</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول خطوط السير -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-route"></i> سجل خطوط السير</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>اسم الرحلة</th>
                                        <th>السائق</th>
                                        <th>النوع</th>
                                        <th>من - إلى</th>
                                        <th>المسافة</th>
                                        <th>التكلفة</th>
                                        <th>الحالة</th>
                                        <th>التقييم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for route in daily_routes %}
                                    <tr>
                                        <td>{{ route.route_date|date:"Y-m-d" }}</td>
                                        <td>
                                            <a href="{% url 'fleet:daily_route_detail' route.pk %}">
                                                <strong>{{ route.route_name }}</strong>
                                            </a>
                                            {% if route.client_name %}
                                                <br>
                                                <small class="text-muted">{{ route.client_name }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:driver_detail' route.driver.pk %}">
                                                {{ route.driver.full_name }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ route.get_route_type_display }}</span>
                                        </td>
                                        <td>
                                            <small>
                                                <strong>من:</strong> {{ route.start_location|truncatechars:20 }}<br>
                                                <strong>إلى:</strong> {{ route.end_location|truncatechars:20 }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if route.actual_distance %}
                                                <strong>{{ route.actual_distance }} كم</strong>
                                                {% if route.estimated_distance %}
                                                    <br>
                                                    <small class="text-muted">مقدر: {{ route.estimated_distance }} كم</small>
                                                {% endif %}
                                            {% elif route.estimated_distance %}
                                                <span class="text-muted">{{ route.estimated_distance }} كم (مقدر)</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if route.actual_cost %}
                                                <strong>{{ route.actual_cost }} ريال</strong>
                                                {% if route.estimated_cost %}
                                                    <br>
                                                    <small class="text-muted">مقدر: {{ route.estimated_cost }} ريال</small>
                                                {% endif %}
                                            {% elif route.estimated_cost %}
                                                <span class="text-muted">{{ route.estimated_cost }} ريال (مقدر)</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ route.status_color }}">{{ route.get_status_display }}</span>
                                            {% if route.is_delayed %}
                                                <br>
                                                <small class="text-warning"><i class="fas fa-clock"></i> متأخر</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if route.is_completed %}
                                                <span class="badge bg-secondary">{{ route.efficiency_rating }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:daily_route_detail' route.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if route.status == 'planned' %}
                                                <a href="{% url 'fleet:daily_route_start' route.pk %}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                            {% elif route.status == 'in_progress' %}
                                                <a href="{% url 'fleet:daily_route_complete' route.pk %}" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="10" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-route fa-3x mb-3"></i>
                                                <h5>لا توجد خطوط سير مسجلة لهذه المركبة</h5>
                                                <p>ابدأ بإنشاء خط السير الأول</p>
                                                <a href="{% url 'fleet:daily_route_create' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إنشاء خط سير جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
