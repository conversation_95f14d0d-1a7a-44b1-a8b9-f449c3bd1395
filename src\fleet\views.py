from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse
from .models import Company, Vehicle, Trip, Maintenance, Incident, UserProfile, Driver, VehicleDriverAssignment
from .forms import DriverForm, VehicleForm, VehicleDriverAssignmentForm


def get_user_profile(user):
    """Helper function to get or create user profile"""
    try:
        return user.profile
    except UserProfile.DoesNotExist:
        # Create a default company if none exists
        default_company, created = Company.objects.get_or_create(
            name="شركة افتراضية",
            defaults={
                'address': 'عنوان افتراضي',
                'phone': '************',
                'activity': 'نشاط افتراضي'
            }
        )

        # Create UserProfile with default values
        profile = UserProfile.objects.create(
            user=user,
            company=default_company,
            role='manager'  # Default role
        )
        return profile


@login_required
def dashboard(request):
    # Get user's company
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    
    # Get company stats
    vehicles_count = Vehicle.objects.filter(company=company).count()
    active_trips = Trip.objects.filter(company=company, status='in_progress').count()
    maintenance_count = Maintenance.objects.filter(
        vehicle__company=company, 
        status__in=['scheduled', 'in_progress']
    ).count()
    incidents_count = Incident.objects.filter(
        vehicle__company=company, 
        status__in=['reported', 'investigating']
    ).count()
    
    context = {
        'company': company,
        'vehicles_count': vehicles_count,
        'active_trips': active_trips,
        'maintenance_count': maintenance_count,
        'incidents_count': incidents_count,
    }
    
    return render(request, 'fleet/dashboard.html', context)

@login_required
def vehicle_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicles = Vehicle.objects.filter(company=company)
    
    return render(request, 'fleet/vehicle_list.html', {'vehicles': vehicles})

@login_required
def vehicle_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicle = get_object_or_404(Vehicle, pk=pk, company=company)
    
    # Get related data
    maintenance_records = vehicle.maintenance_records.all().order_by('-scheduled_date')
    trips = vehicle.trips.all().order_by('-start_time')
    incidents = vehicle.incidents.all().order_by('-date_time')
    
    context = {
        'vehicle': vehicle,
        'maintenance_records': maintenance_records,
        'trips': trips,
        'incidents': incidents,
    }
    
    return render(request, 'fleet/vehicle_detail.html', context)


@login_required
def vehicle_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = VehicleForm(request.POST)
        if form.is_valid():
            vehicle = form.save(commit=False)
            vehicle.company = company
            vehicle.save()
            messages.success(request, 'تم إضافة المركبة بنجاح!')
            return redirect('fleet:vehicle_detail', pk=vehicle.pk)
    else:
        form = VehicleForm()

    return render(request, 'fleet/vehicle_form.html', {
        'form': form,
        'title': 'إضافة مركبة جديدة',
        'submit_text': 'إضافة المركبة'
    })


@login_required
def vehicle_edit(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicle = get_object_or_404(Vehicle, pk=pk, company=company)

    if request.method == 'POST':
        form = VehicleForm(request.POST, instance=vehicle)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المركبة بنجاح!')
            return redirect('fleet:vehicle_detail', pk=vehicle.pk)
    else:
        form = VehicleForm(instance=vehicle)

    return render(request, 'fleet/vehicle_form.html', {
        'form': form,
        'vehicle': vehicle,
        'title': f'تعديل المركبة {vehicle.plate_number}',
        'submit_text': 'حفظ التغييرات'
    })


@login_required
def driver_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    drivers = Driver.objects.filter(company=company).order_by('first_name', 'last_name')

    return render(request, 'fleet/driver_list.html', {'drivers': drivers})


@login_required
def driver_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    driver = get_object_or_404(Driver, pk=pk, company=company)

    # Get driver's vehicle assignments
    assignments = VehicleDriverAssignment.objects.filter(driver=driver).order_by('-start_date')
    current_assignment = assignments.filter(end_date__isnull=True).first()

    # Get driver's trips
    trips = Trip.objects.filter(driver=driver).order_by('-start_time')[:10]

    # Get driver's incidents
    incidents = Incident.objects.filter(driver=driver).order_by('-date_time')[:10]

    context = {
        'driver': driver,
        'assignments': assignments,
        'current_assignment': current_assignment,
        'trips': trips,
        'incidents': incidents,
    }

    return render(request, 'fleet/driver_detail.html', context)


@login_required
def driver_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = DriverForm(request.POST)
        if form.is_valid():
            driver = form.save(commit=False)
            driver.company = company
            driver.save()
            messages.success(request, 'تم إضافة السائق بنجاح!')
            return redirect('fleet:driver_detail', pk=driver.pk)
    else:
        form = DriverForm()

    return render(request, 'fleet/driver_form.html', {
        'form': form,
        'title': 'إضافة سائق جديد',
        'submit_text': 'إضافة السائق'
    })


@login_required
def driver_edit(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    driver = get_object_or_404(Driver, pk=pk, company=company)

    if request.method == 'POST':
        form = DriverForm(request.POST, instance=driver)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات السائق بنجاح!')
            return redirect('fleet:driver_detail', pk=driver.pk)
    else:
        form = DriverForm(instance=driver)

    return render(request, 'fleet/driver_form.html', {
        'form': form,
        'driver': driver,
        'title': f'تعديل بيانات {driver.full_name}',
        'submit_text': 'حفظ التغييرات'
    })


@login_required
def vehicle_assignments(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    # Get all assignments for company vehicles
    assignments = VehicleDriverAssignment.objects.filter(
        vehicle__company=company
    ).select_related('vehicle', 'driver', 'assigned_by').order_by('-start_date')

    # Get active assignments
    active_assignments = assignments.filter(end_date__isnull=True)

    context = {
        'assignments': assignments,
        'active_assignments': active_assignments,
    }

    return render(request, 'fleet/vehicle_assignments.html', context)


@login_required
def assignment_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = VehicleDriverAssignmentForm(request.POST, company=company)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.assigned_by = user_profile
            assignment.save()
            messages.success(request, 'تم إنشاء التعيين بنجاح!')
            return redirect('fleet:vehicle_assignments')
    else:
        form = VehicleDriverAssignmentForm(company=company)

    return render(request, 'fleet/assignment_form.html', {
        'form': form,
        'title': 'تعيين سائق لمركبة',
        'submit_text': 'إنشاء التعيين'
    })


@login_required
def assignment_end(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    assignment = get_object_or_404(
        VehicleDriverAssignment,
        pk=pk,
        vehicle__company=company,
        end_date__isnull=True
    )

    if request.method == 'POST':
        from django.utils import timezone
        assignment.end_date = timezone.now()
        assignment.status = 'completed'
        assignment.save()
        messages.success(request, 'تم إنهاء التعيين بنجاح!')
        return redirect('fleet:vehicle_assignments')

    return render(request, 'fleet/assignment_end_confirm.html', {
        'assignment': assignment
    })
