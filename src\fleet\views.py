from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Company, Vehicle, Trip, Maintenance, Incident

@login_required
def dashboard(request):
    # Get user's company
    user_profile = request.user.profile
    company = user_profile.company
    
    # Get company stats
    vehicles_count = Vehicle.objects.filter(company=company).count()
    active_trips = Trip.objects.filter(company=company, status='in_progress').count()
    maintenance_count = Maintenance.objects.filter(
        vehicle__company=company, 
        status__in=['scheduled', 'in_progress']
    ).count()
    incidents_count = Incident.objects.filter(
        vehicle__company=company, 
        status__in=['reported', 'investigating']
    ).count()
    
    context = {
        'company': company,
        'vehicles_count': vehicles_count,
        'active_trips': active_trips,
        'maintenance_count': maintenance_count,
        'incidents_count': incidents_count,
    }
    
    return render(request, 'fleet/dashboard.html', context)

@login_required
def vehicle_list(request):
    user_profile = request.user.profile
    company = user_profile.company
    vehicles = Vehicle.objects.filter(company=company)
    
    return render(request, 'fleet/vehicle_list.html', {'vehicles': vehicles})

@login_required
def vehicle_detail(request, pk):
    user_profile = request.user.profile
    company = user_profile.company
    vehicle = get_object_or_404(Vehicle, pk=pk, company=company)
    
    # Get related data
    maintenance_records = vehicle.maintenance_records.all().order_by('-scheduled_date')
    trips = vehicle.trips.all().order_by('-start_time')
    incidents = vehicle.incidents.all().order_by('-date_time')
    
    context = {
        'vehicle': vehicle,
        'maintenance_records': maintenance_records,
        'trips': trips,
        'incidents': incidents,
    }
    
    return render(request, 'fleet/vehicle_detail.html', context)
