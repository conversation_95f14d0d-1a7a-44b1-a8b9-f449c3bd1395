from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.db import models
from .models import Company, Vehicle, Trip, Maintenance, Incident, UserProfile, Driver, VehicleDriverAssignment, FuelConsumption, DailyOdometerReading
from .forms import DriverForm, VehicleForm, VehicleDriverAssignmentForm, FuelConsumptionForm, DailyOdometerReadingForm


def get_user_profile(user):
    """Helper function to get or create user profile"""
    try:
        return user.profile
    except UserProfile.DoesNotExist:
        # Create a default company if none exists
        default_company, created = Company.objects.get_or_create(
            name="شركة افتراضية",
            defaults={
                'address': 'عنوان افتراضي',
                'phone': '************',
                'activity': 'نشاط افتراضي'
            }
        )

        # Create UserProfile with default values
        profile = UserProfile.objects.create(
            user=user,
            company=default_company,
            role='manager'  # Default role
        )
        return profile


@login_required
def dashboard(request):
    # Get user's company
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    
    # Get company stats
    vehicles_count = Vehicle.objects.filter(company=company).count()
    active_trips = Trip.objects.filter(company=company, status='in_progress').count()
    maintenance_count = Maintenance.objects.filter(
        vehicle__company=company,
        status__in=['scheduled', 'in_progress']
    ).count()
    incidents_count = Incident.objects.filter(
        vehicle__company=company,
        status__in=['reported', 'investigating']
    ).count()

    # إحصائيات استهلاك الوقود
    fuel_measurements_count = FuelConsumption.objects.filter(vehicle__company=company).count()
    if fuel_measurements_count > 0:
        avg_fuel_consumption = FuelConsumption.objects.filter(
            vehicle__company=company
        ).aggregate(avg=models.Avg('consumption_rate'))['avg']
        avg_fuel_consumption = round(avg_fuel_consumption, 2) if avg_fuel_consumption else 0
    else:
        avg_fuel_consumption = 0

    context = {
        'company': company,
        'vehicles_count': vehicles_count,
        'active_trips': active_trips,
        'maintenance_count': maintenance_count,
        'incidents_count': incidents_count,
        'fuel_measurements_count': fuel_measurements_count,
        'avg_fuel_consumption': avg_fuel_consumption,
    }
    
    return render(request, 'fleet/dashboard.html', context)

@login_required
def vehicle_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicles = Vehicle.objects.filter(company=company)
    
    return render(request, 'fleet/vehicle_list.html', {'vehicles': vehicles})

@login_required
def vehicle_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicle = get_object_or_404(Vehicle, pk=pk, company=company)
    
    # Get related data
    maintenance_records = vehicle.maintenance_records.all().order_by('-scheduled_date')
    trips = vehicle.trips.all().order_by('-start_time')
    incidents = vehicle.incidents.all().order_by('-date_time')
    
    context = {
        'vehicle': vehicle,
        'maintenance_records': maintenance_records,
        'trips': trips,
        'incidents': incidents,
    }
    
    return render(request, 'fleet/vehicle_detail.html', context)


@login_required
def vehicle_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = VehicleForm(request.POST)
        if form.is_valid():
            vehicle = form.save(commit=False)
            vehicle.company = company
            vehicle.save()
            messages.success(request, 'تم إضافة المركبة بنجاح!')
            return redirect('fleet:vehicle_detail', pk=vehicle.pk)
    else:
        form = VehicleForm()

    return render(request, 'fleet/vehicle_form.html', {
        'form': form,
        'title': 'إضافة مركبة جديدة',
        'submit_text': 'إضافة المركبة'
    })


@login_required
def vehicle_edit(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    vehicle = get_object_or_404(Vehicle, pk=pk, company=company)

    if request.method == 'POST':
        form = VehicleForm(request.POST, instance=vehicle)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المركبة بنجاح!')
            return redirect('fleet:vehicle_detail', pk=vehicle.pk)
    else:
        form = VehicleForm(instance=vehicle)

    return render(request, 'fleet/vehicle_form.html', {
        'form': form,
        'vehicle': vehicle,
        'title': f'تعديل المركبة {vehicle.plate_number}',
        'submit_text': 'حفظ التغييرات'
    })


@login_required
def driver_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    drivers = Driver.objects.filter(company=company).order_by('first_name', 'last_name')

    return render(request, 'fleet/driver_list.html', {'drivers': drivers})


@login_required
def driver_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    driver = get_object_or_404(Driver, pk=pk, company=company)

    # Get driver's vehicle assignments
    assignments = VehicleDriverAssignment.objects.filter(driver=driver).order_by('-start_date')
    current_assignment = assignments.filter(end_date__isnull=True).first()

    # Get driver's trips
    trips = Trip.objects.filter(driver=driver).order_by('-start_time')[:10]

    # Get driver's incidents
    incidents = Incident.objects.filter(driver=driver).order_by('-date_time')[:10]

    context = {
        'driver': driver,
        'assignments': assignments,
        'current_assignment': current_assignment,
        'trips': trips,
        'incidents': incidents,
    }

    return render(request, 'fleet/driver_detail.html', context)


@login_required
def driver_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = DriverForm(request.POST)
        if form.is_valid():
            driver = form.save(commit=False)
            driver.company = company
            driver.save()
            messages.success(request, 'تم إضافة السائق بنجاح!')
            return redirect('fleet:driver_detail', pk=driver.pk)
    else:
        form = DriverForm()

    return render(request, 'fleet/driver_form.html', {
        'form': form,
        'title': 'إضافة سائق جديد',
        'submit_text': 'إضافة السائق'
    })


@login_required
def driver_edit(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company
    driver = get_object_or_404(Driver, pk=pk, company=company)

    if request.method == 'POST':
        form = DriverForm(request.POST, instance=driver)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات السائق بنجاح!')
            return redirect('fleet:driver_detail', pk=driver.pk)
    else:
        form = DriverForm(instance=driver)

    return render(request, 'fleet/driver_form.html', {
        'form': form,
        'driver': driver,
        'title': f'تعديل بيانات {driver.full_name}',
        'submit_text': 'حفظ التغييرات'
    })


@login_required
def vehicle_assignments(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    # Get all assignments for company vehicles
    assignments = VehicleDriverAssignment.objects.filter(
        vehicle__company=company
    ).select_related('vehicle', 'driver', 'assigned_by').order_by('-start_date')

    # Get active assignments
    active_assignments = assignments.filter(end_date__isnull=True)

    context = {
        'assignments': assignments,
        'active_assignments': active_assignments,
    }

    return render(request, 'fleet/vehicle_assignments.html', context)


@login_required
def assignment_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = VehicleDriverAssignmentForm(request.POST, company=company)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.assigned_by = user_profile
            assignment.save()
            messages.success(request, 'تم إنشاء التعيين بنجاح!')
            return redirect('fleet:vehicle_assignments')
    else:
        form = VehicleDriverAssignmentForm(company=company)

    return render(request, 'fleet/assignment_form.html', {
        'form': form,
        'title': 'تعيين سائق لمركبة',
        'submit_text': 'إنشاء التعيين'
    })


@login_required
def assignment_end(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    assignment = get_object_or_404(
        VehicleDriverAssignment,
        pk=pk,
        vehicle__company=company,
        end_date__isnull=True
    )

    if request.method == 'POST':
        from django.utils import timezone
        assignment.end_date = timezone.now()
        assignment.status = 'completed'
        assignment.save()
        messages.success(request, 'تم إنهاء التعيين بنجاح!')
        return redirect('fleet:vehicle_assignments')

    return render(request, 'fleet/assignment_end_confirm.html', {
        'assignment': assignment
    })


@login_required
def fuel_consumption_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    # الحصول على جميع قياسات استهلاك الوقود للشركة
    fuel_consumptions = FuelConsumption.objects.filter(
        vehicle__company=company
    ).select_related('vehicle', 'recorded_by').order_by('-measurement_date')

    # إحصائيات سريعة
    total_measurements = fuel_consumptions.count()
    if total_measurements > 0:
        avg_consumption = fuel_consumptions.aggregate(
            avg=models.Avg('consumption_rate')
        )['avg']
        best_consumption = fuel_consumptions.aggregate(
            min=models.Min('consumption_rate')
        )['min']
        worst_consumption = fuel_consumptions.aggregate(
            max=models.Max('consumption_rate')
        )['max']
    else:
        avg_consumption = best_consumption = worst_consumption = None

    context = {
        'fuel_consumptions': fuel_consumptions,
        'total_measurements': total_measurements,
        'avg_consumption': round(avg_consumption, 2) if avg_consumption else None,
        'best_consumption': best_consumption,
        'worst_consumption': worst_consumption,
    }

    return render(request, 'fleet/fuel_consumption_list.html', context)


@login_required
def fuel_consumption_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = FuelConsumptionForm(request.POST, company=company)
        if form.is_valid():
            fuel_consumption = form.save(commit=False)
            fuel_consumption.recorded_by = user_profile
            fuel_consumption.save()
            messages.success(request, 'تم تسجيل قياس استهلاك الوقود بنجاح!')
            return redirect('fleet:fuel_consumption_list')
    else:
        form = FuelConsumptionForm(company=company)

    return render(request, 'fleet/fuel_consumption_form.html', {
        'form': form,
        'title': 'تسجيل قياس استهلاك الوقود',
        'submit_text': 'حفظ القياس'
    })


@login_required
def fuel_consumption_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    fuel_consumption = get_object_or_404(
        FuelConsumption,
        pk=pk,
        vehicle__company=company
    )

    # الحصول على قياسات أخرى لنفس المركبة
    other_measurements = FuelConsumption.objects.filter(
        vehicle=fuel_consumption.vehicle
    ).exclude(pk=pk).order_by('-measurement_date')[:5]

    context = {
        'fuel_consumption': fuel_consumption,
        'other_measurements': other_measurements,
    }

    return render(request, 'fleet/fuel_consumption_detail.html', context)


@login_required
def vehicle_fuel_history(request, vehicle_pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    vehicle = get_object_or_404(Vehicle, pk=vehicle_pk, company=company)

    # الحصول على جميع قياسات الوقود للمركبة
    fuel_consumptions = FuelConsumption.objects.filter(
        vehicle=vehicle
    ).order_by('-measurement_date')

    # حساب الإحصائيات
    if fuel_consumptions:
        avg_consumption = fuel_consumptions.aggregate(
            avg=models.Avg('consumption_rate')
        )['avg']
        best_consumption = fuel_consumptions.aggregate(
            min=models.Min('consumption_rate')
        )['min']
        worst_consumption = fuel_consumptions.aggregate(
            max=models.Max('consumption_rate')
        )['max']

        # اتجاه الاستهلاك (تحسن أم تدهور)
        recent_measurements = fuel_consumptions[:3]
        if len(recent_measurements) >= 2:
            recent_avg = sum(m.consumption_rate for m in recent_measurements) / len(recent_measurements)
            older_measurements = fuel_consumptions[3:6]
            if older_measurements:
                older_avg = sum(m.consumption_rate for m in older_measurements) / len(older_measurements)
                trend = 'تحسن' if recent_avg < older_avg else 'تدهور' if recent_avg > older_avg else 'ثابت'
            else:
                trend = 'غير محدد'
        else:
            trend = 'غير محدد'
    else:
        avg_consumption = best_consumption = worst_consumption = None
        trend = 'لا توجد بيانات'

    context = {
        'vehicle': vehicle,
        'fuel_consumptions': fuel_consumptions,
        'avg_consumption': round(avg_consumption, 2) if avg_consumption else None,
        'best_consumption': best_consumption,
        'worst_consumption': worst_consumption,
        'trend': trend,
    }

    return render(request, 'fleet/vehicle_fuel_history.html', context)


@login_required
def odometer_reading_list(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    # الحصول على جميع قراءات العداد للشركة
    odometer_readings = DailyOdometerReading.objects.filter(
        vehicle__company=company
    ).select_related('vehicle', 'driver').order_by('-reading_date', '-created_at')

    # إحصائيات سريعة
    total_readings = odometer_readings.count()
    pending_readings = odometer_readings.filter(status='pending').count()
    verified_readings = odometer_readings.filter(status='verified').count()
    rejected_readings = odometer_readings.filter(status='rejected').count()

    # قراءات اليوم
    from django.utils import timezone
    today = timezone.now().date()
    today_readings = odometer_readings.filter(reading_date=today).count()

    context = {
        'odometer_readings': odometer_readings,
        'total_readings': total_readings,
        'pending_readings': pending_readings,
        'verified_readings': verified_readings,
        'rejected_readings': rejected_readings,
        'today_readings': today_readings,
    }

    return render(request, 'fleet/odometer_reading_list.html', context)


@login_required
def odometer_reading_create(request):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    if request.method == 'POST':
        form = DailyOdometerReadingForm(request.POST, request.FILES, company=company, user_profile=user_profile)
        if form.is_valid():
            odometer_reading = form.save()
            messages.success(request, 'تم تسجيل قراءة العداد بنجاح!')
            return redirect('fleet:odometer_reading_detail', pk=odometer_reading.pk)
    else:
        form = DailyOdometerReadingForm(company=company, user_profile=user_profile)

        # تعيين التاريخ الحالي كقيمة افتراضية
        from django.utils import timezone
        form.fields['reading_date'].initial = timezone.now().date()

    return render(request, 'fleet/odometer_reading_form.html', {
        'form': form,
        'title': 'تسجيل قراءة عداد جديدة',
        'submit_text': 'حفظ القراءة'
    })


@login_required
def odometer_reading_detail(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    odometer_reading = get_object_or_404(
        DailyOdometerReading,
        pk=pk,
        vehicle__company=company
    )

    # الحصول على قراءات أخرى لنفس المركبة
    other_readings = DailyOdometerReading.objects.filter(
        vehicle=odometer_reading.vehicle
    ).exclude(pk=pk).order_by('-reading_date')[:10]

    context = {
        'odometer_reading': odometer_reading,
        'other_readings': other_readings,
    }

    return render(request, 'fleet/odometer_reading_detail.html', context)


@login_required
def odometer_reading_verify(request, pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    odometer_reading = get_object_or_404(
        DailyOdometerReading,
        pk=pk,
        vehicle__company=company,
        status='pending'
    )

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'verify':
            from django.utils import timezone
            odometer_reading.status = 'verified'
            odometer_reading.verified_by = user_profile
            odometer_reading.verification_date = timezone.now()
            odometer_reading.save()
            messages.success(request, 'تم التحقق من قراءة العداد بنجاح!')

        elif action == 'reject':
            rejection_reason = request.POST.get('rejection_reason', '')
            if rejection_reason:
                odometer_reading.status = 'rejected'
                odometer_reading.verified_by = user_profile
                odometer_reading.verification_date = timezone.now()
                odometer_reading.rejection_reason = rejection_reason
                odometer_reading.save()
                messages.success(request, 'تم رفض قراءة العداد!')
            else:
                messages.error(request, 'يجب إدخال سبب الرفض')
                return render(request, 'fleet/odometer_reading_verify.html', {
                    'odometer_reading': odometer_reading
                })

        return redirect('fleet:odometer_reading_detail', pk=odometer_reading.pk)

    return render(request, 'fleet/odometer_reading_verify.html', {
        'odometer_reading': odometer_reading
    })


@login_required
def vehicle_odometer_history(request, vehicle_pk):
    user_profile = get_user_profile(request.user)
    company = user_profile.company

    vehicle = get_object_or_404(Vehicle, pk=vehicle_pk, company=company)

    # الحصول على جميع قراءات العداد للمركبة
    odometer_readings = DailyOdometerReading.objects.filter(
        vehicle=vehicle
    ).order_by('-reading_date')

    # حساب الإحصائيات
    verified_readings = odometer_readings.filter(status='verified')

    if verified_readings:
        # إجمالي المسافة المقطوعة
        total_distance = sum(r.daily_distance for r in verified_readings if r.daily_distance)

        # متوسط المسافة اليومية
        daily_distances = [r.daily_distance for r in verified_readings if r.daily_distance]
        avg_daily_distance = sum(daily_distances) / len(daily_distances) if daily_distances else 0

        # أعلى وأقل مسافة يومية
        max_daily_distance = max(daily_distances) if daily_distances else 0
        min_daily_distance = min(daily_distances) if daily_distances else 0

        # آخر قراءة
        latest_reading = verified_readings.first()
        current_odometer = latest_reading.odometer_reading if latest_reading else 0
    else:
        total_distance = avg_daily_distance = max_daily_distance = min_daily_distance = current_odometer = 0

    context = {
        'vehicle': vehicle,
        'odometer_readings': odometer_readings,
        'total_distance': total_distance,
        'avg_daily_distance': round(avg_daily_distance, 1),
        'max_daily_distance': max_daily_distance,
        'min_daily_distance': min_daily_distance,
        'current_odometer': current_odometer,
    }

    return render(request, 'fleet/vehicle_odometer_history.html', context)
