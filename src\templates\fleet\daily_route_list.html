<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطوط السير اليومية - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">خطوط السير اليومية</h1>
                    <div>
                        <a href="{% url 'fleet:daily_route_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إنشاء خط سير جديد
                        </a>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">{{ total_routes }}</h5>
                                <p class="card-text">إجمالي الرحلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">{{ planned_routes }}</h5>
                                <p class="card-text">مخططة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">{{ in_progress_routes }}</h5>
                                <p class="card-text">قيد التنفيذ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">{{ completed_routes }}</h5>
                                <p class="card-text">مكتملة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">{{ cancelled_routes }}</h5>
                                <p class="card-text">ملغية</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات اليوم -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-calendar-day"></i> رحلات اليوم</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h5 class="text-primary">{{ today_total }}</h5>
                                        <p class="text-muted">إجمالي رحلات اليوم</p>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success">{{ today_completed }}</h5>
                                        <p class="text-muted">مكتملة اليوم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول خطوط السير -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-route"></i> خطوط السير اليومية</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم الرحلة</th>
                                        <th>المركبة</th>
                                        <th>السائق</th>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الأولوية</th>
                                        <th>الوقت المخطط</th>
                                        <th>الحالة</th>
                                        <th>التقييم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for route in daily_routes %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'fleet:daily_route_detail' route.pk %}">
                                                <strong>{{ route.route_name }}</strong>
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ route.client_name|default:"" }}</small>
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:vehicle_detail' route.vehicle.pk %}">
                                                {{ route.vehicle.plate_number }}
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ route.vehicle.model }}</small>
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:driver_detail' route.driver.pk %}">
                                                {{ route.driver.full_name }}
                                            </a>
                                        </td>
                                        <td>{{ route.route_date|date:"Y-m-d" }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ route.get_route_type_display }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ route.priority_color }}">{{ route.get_priority_display }}</span>
                                        </td>
                                        <td>
                                            {{ route.planned_start_time }} - {{ route.planned_end_time }}
                                            {% if route.actual_start_time %}
                                                <br>
                                                <small class="text-success">فعلي: {{ route.actual_start_time }}{% if route.actual_end_time %} - {{ route.actual_end_time }}{% endif %}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ route.status_color }}">{{ route.get_status_display }}</span>
                                            {% if route.is_delayed %}
                                                <br>
                                                <small class="text-warning"><i class="fas fa-clock"></i> متأخر</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if route.is_completed %}
                                                <span class="badge bg-secondary">{{ route.efficiency_rating }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:daily_route_detail' route.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            {% if route.status == 'planned' %}
                                                <a href="{% url 'fleet:daily_route_start' route.pk %}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-play"></i> بدء
                                                </a>
                                            {% elif route.status == 'in_progress' %}
                                                <a href="{% url 'fleet:daily_route_complete' route.pk %}" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-check"></i> إكمال
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="10" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-route fa-3x mb-3"></i>
                                                <h5>لا توجد خطوط سير مسجلة</h5>
                                                <p>ابدأ بإنشاء خط السير الأول</p>
                                                <a href="{% url 'fleet:daily_route_create' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> إنشاء خط سير جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
