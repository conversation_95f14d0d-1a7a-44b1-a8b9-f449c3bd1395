# Generated by Django 5.2.3 on 2025-06-10 12:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fleet', '0004_dailyodometerreading'),
    ]

    operations = [
        migrations.CreateModel(
            name='FuelRefill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refill_date', models.DateTimeField(verbose_name='تاريخ ووقت التزود')),
                ('fuel_type', models.CharField(choices=[('gasoline_91', 'بنزين 91'), ('gasoline_95', 'بنزين 95'), ('diesel', 'ديزل'), ('premium', 'ممتاز')], max_length=20, verbose_name='نوع الوقود')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='الكمية (لتر)')),
                ('price_per_liter', models.DecimalField(decimal_places=3, max_digits=6, verbose_name='سعر اللتر (ريال)')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ الإجمالي (ريال)')),
                ('odometer_reading', models.PositiveIntegerField(verbose_name='قراءة العداد (كم)')),
                ('station_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم المحطة')),
                ('station_location', models.CharField(blank=True, max_length=300, null=True, verbose_name='موقع المحطة')),
                ('pump_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم المضخة')),
                ('pump_receipt_image', models.ImageField(blank=True, null=True, upload_to='fuel_refills/%Y/%m/%d/', verbose_name='صورة إيصال المضخة')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('visa', 'فيزا'), ('mastercard', 'ماستركارد'), ('company_card', 'بطاقة الشركة'), ('fuel_card', 'بطاقة وقود')], max_length=20, verbose_name='طريقة الدفع')),
                ('card_last_four', models.CharField(blank=True, max_length=4, null=True, verbose_name='آخر 4 أرقام من البطاقة')),
                ('card_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع البطاقة')),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المعاملة')),
                ('approval_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='رمز الموافقة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('receipt_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم الإيصال')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('under_review', 'قيد المراجعة')], default='pending', max_length=20, verbose_name='حالة التسجيل')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='سبب الرفض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_refills', to='fleet.userprofile', verbose_name='تمت الموافقة بواسطة')),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fuel_refills', to='fleet.driver', verbose_name='السائق')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fuel_refills', to='fleet.vehicle', verbose_name='المركبة')),
            ],
            options={
                'verbose_name': 'تزود بالوقود',
                'verbose_name_plural': 'عمليات التزود بالوقود',
                'ordering': ['-refill_date'],
                'indexes': [models.Index(fields=['vehicle', '-refill_date'], name='fleet_fuelr_vehicle_3d36fe_idx'), models.Index(fields=['driver', '-refill_date'], name='fleet_fuelr_driver__a8c52a_idx'), models.Index(fields=['refill_date'], name='fleet_fuelr_refill__43b1c3_idx'), models.Index(fields=['status'], name='fleet_fuelr_status_527f2e_idx')],
            },
        ),
    ]
