<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة السائقين - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">قائمة السائقين</h1>
                    <a href="{% url 'fleet:driver_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة سائق جديد
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>رقم الهوية</th>
                                <th>رقم الرخصة</th>
                                <th>نوع الرخصة</th>
                                <th>رقم الهاتف</th>
                                <th>الحالة</th>
                                <th>تاريخ التوظيف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for driver in drivers %}
                            <tr>
                                <td>
                                    <strong>{{ driver.full_name }}</strong>
                                    {% if driver.is_license_expired %}
                                        <span class="badge bg-danger ms-2">رخصة منتهية</span>
                                    {% endif %}
                                </td>
                                <td>{{ driver.national_id }}</td>
                                <td>{{ driver.license_number }}</td>
                                <td>
                                    {% if driver.license_type == 'private' %}
                                        رخصة خاصة
                                    {% elif driver.license_type == 'public' %}
                                        رخصة عامة
                                    {% elif driver.license_type == 'heavy' %}
                                        رخصة ثقيلة
                                    {% else %}
                                        رخصة دراجة نارية
                                    {% endif %}
                                </td>
                                <td>{{ driver.phone }}</td>
                                <td>
                                    {% if driver.status == 'active' %}
                                        <span class="badge bg-success">نشط</span>
                                    {% elif driver.status == 'inactive' %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% elif driver.status == 'suspended' %}
                                        <span class="badge bg-danger">موقوف</span>
                                    {% else %}
                                        <span class="badge bg-warning">في إجازة</span>
                                    {% endif %}
                                </td>
                                <td>{{ driver.hire_date }}</td>
                                <td>
                                    <a href="{% url 'fleet:driver_detail' driver.pk %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{% url 'fleet:driver_edit' driver.pk %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="text-center text-muted">لا يوجد سائقين مسجلين</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي السائقين</h5>
                                <h2 class="text-primary">{{ drivers.count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">السائقين النشطين</h5>
                                <h2 class="text-success">{{ drivers|length }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">رخص منتهية</h5>
                                <h2 class="text-danger">
                                    {% for driver in drivers %}
                                        {% if driver.is_license_expired %}1{% endif %}
                                    {% empty %}0{% endfor %}
                                </h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">في إجازة</h5>
                                <h2 class="text-warning">
                                    {{ drivers|length }}
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
