<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل خط السير - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ daily_route.route_name }}</h1>
                    <div>
                        <a href="{% url 'fleet:daily_route_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        {% if daily_route.status == 'planned' %}
                            <a href="{% url 'fleet:daily_route_start' daily_route.pk %}" class="btn btn-success">
                                <i class="fas fa-play"></i> بدء الرحلة
                            </a>
                        {% elif daily_route.status == 'in_progress' %}
                            <a href="{% url 'fleet:daily_route_complete' daily_route.pk %}" class="btn btn-warning">
                                <i class="fas fa-check"></i> إكمال الرحلة
                            </a>
                        {% endif %}
                    </div>
                </div>

                <!-- معلومات أساسية -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> معلومات الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>المركبة:</strong> 
                                            <a href="{% url 'fleet:vehicle_detail' daily_route.vehicle.pk %}">
                                                {{ daily_route.vehicle.plate_number }} - {{ daily_route.vehicle.model }}
                                            </a>
                                        </p>
                                        <p><strong>السائق:</strong> 
                                            <a href="{% url 'fleet:driver_detail' daily_route.driver.pk %}">
                                                {{ daily_route.driver.full_name }}
                                            </a>
                                        </p>
                                        <p><strong>تاريخ الرحلة:</strong> {{ daily_route.route_date|date:"Y-m-d" }}</p>
                                        <p><strong>نوع الرحلة:</strong> 
                                            <span class="badge bg-info">{{ daily_route.get_route_type_display }}</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>الأولوية:</strong> 
                                            <span class="badge bg-{{ daily_route.priority_color }}">{{ daily_route.get_priority_display }}</span>
                                        </p>
                                        <p><strong>الحالة:</strong> 
                                            <span class="badge bg-{{ daily_route.status_color }}">{{ daily_route.get_status_display }}</span>
                                        </p>
                                        {% if daily_route.is_completed %}
                                            <p><strong>تقييم الكفاءة:</strong> 
                                                <span class="badge bg-secondary">{{ daily_route.efficiency_rating }}</span>
                                            </p>
                                        {% endif %}
                                        {% if daily_route.created_by %}
                                            <p><strong>أنشئ بواسطة:</strong> {{ daily_route.created_by.user.get_full_name|default:daily_route.created_by.user.username }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-line"></i> إحصائيات سريعة</h6>
                            </div>
                            <div class="card-body">
                                {% if daily_route.estimated_distance %}
                                    <p><strong>المسافة المقدرة:</strong> {{ daily_route.estimated_distance }} كم</p>
                                {% endif %}
                                {% if daily_route.actual_distance %}
                                    <p><strong>المسافة الفعلية:</strong> {{ daily_route.actual_distance }} كم</p>
                                {% endif %}
                                {% if daily_route.estimated_cost %}
                                    <p><strong>التكلفة المقدرة:</strong> {{ daily_route.estimated_cost }} ريال</p>
                                {% endif %}
                                {% if daily_route.actual_cost %}
                                    <p><strong>التكلفة الفعلية:</strong> {{ daily_route.actual_cost }} ريال</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نقاط البداية والنهاية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-map-marker-alt text-primary"></i> نقطة البداية</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الموقع:</strong> {{ daily_route.start_location }}</p>
                                {% if daily_route.start_latitude and daily_route.start_longitude %}
                                    <p><strong>الإحداثيات:</strong> {{ daily_route.start_latitude }}, {{ daily_route.start_longitude }}</p>
                                    <a href="https://maps.google.com/?q={{ daily_route.start_latitude }},{{ daily_route.start_longitude }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-map"></i> عرض في الخريطة
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-map-marker-alt text-success"></i> نقطة النهاية</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الموقع:</strong> {{ daily_route.end_location }}</p>
                                {% if daily_route.end_latitude and daily_route.end_longitude %}
                                    <p><strong>الإحداثيات:</strong> {{ daily_route.end_latitude }}, {{ daily_route.end_longitude }}</p>
                                    <a href="https://maps.google.com/?q={{ daily_route.end_latitude }},{{ daily_route.end_longitude }}" target="_blank" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-map"></i> عرض في الخريطة
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأوقات -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> الأوقات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">الأوقات المخططة</h6>
                                <p><strong>البداية:</strong> {{ daily_route.planned_start_time }}</p>
                                <p><strong>النهاية:</strong> {{ daily_route.planned_end_time }}</p>
                                {% if daily_route.planned_duration %}
                                    <p><strong>المدة المخططة:</strong> {{ daily_route.planned_duration }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">الأوقات الفعلية</h6>
                                {% if daily_route.actual_start_time %}
                                    <p><strong>البداية:</strong> {{ daily_route.actual_start_time }}</p>
                                {% else %}
                                    <p><strong>البداية:</strong> <span class="text-muted">لم تبدأ بعد</span></p>
                                {% endif %}
                                
                                {% if daily_route.actual_end_time %}
                                    <p><strong>النهاية:</strong> {{ daily_route.actual_end_time }}</p>
                                {% else %}
                                    <p><strong>النهاية:</strong> <span class="text-muted">لم تنته بعد</span></p>
                                {% endif %}
                                
                                {% if daily_route.actual_duration %}
                                    <p><strong>المدة الفعلية:</strong> {{ daily_route.actual_duration }}</p>
                                {% endif %}
                                
                                {% if daily_route.is_delayed %}
                                    <p><strong>التأخير:</strong> 
                                        <span class="text-warning">{{ daily_route.delay_duration }}</span>
                                    </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قراءات العداد -->
                {% if daily_route.start_odometer or daily_route.end_odometer %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tachometer-alt"></i> قراءات العداد</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <p><strong>قراءة البداية:</strong> 
                                    {% if daily_route.start_odometer %}
                                        {{ daily_route.start_odometer }} كم
                                    {% else %}
                                        <span class="text-muted">غير مسجلة</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>قراءة النهاية:</strong> 
                                    {% if daily_route.end_odometer %}
                                        {{ daily_route.end_odometer }} كم
                                    {% else %}
                                        <span class="text-muted">غير مسجلة</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>المسافة المقطوعة:</strong> 
                                    {% if daily_route.actual_distance %}
                                        <strong class="text-success">{{ daily_route.actual_distance }} كم</strong>
                                    {% else %}
                                        <span class="text-muted">غير محسوبة</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- معلومات العميل -->
                {% if daily_route.client_name %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-user"></i> معلومات العميل</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <p><strong>اسم العميل:</strong> {{ daily_route.client_name }}</p>
                            </div>
                            <div class="col-md-4">
                                {% if daily_route.client_phone %}
                                    <p><strong>الهاتف:</strong> 
                                        <a href="tel:{{ daily_route.client_phone }}">{{ daily_route.client_phone }}</a>
                                    </p>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                {% if daily_route.client_address %}
                                    <p><strong>العنوان:</strong> {{ daily_route.client_address }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- النقاط الوسطية -->
                {% if waypoints %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-map-signs"></i> النقاط الوسطية</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>اسم النقطة</th>
                                        <th>النوع</th>
                                        <th>العنوان</th>
                                        <th>الوقت المخطط</th>
                                        <th>الوقت الفعلي</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for waypoint in waypoints %}
                                    <tr>
                                        <td>{{ waypoint.order }}</td>
                                        <td>{{ waypoint.name }}</td>
                                        <td><span class="badge bg-info">{{ waypoint.get_waypoint_type_display }}</span></td>
                                        <td>{{ waypoint.address }}</td>
                                        <td>{{ waypoint.planned_arrival_time }}</td>
                                        <td>
                                            {% if waypoint.actual_arrival_time %}
                                                {{ waypoint.actual_arrival_time }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ waypoint.status_color }}">{{ waypoint.get_status_display }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- الوصف والملاحظات -->
                {% if daily_route.description or daily_route.notes %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-sticky-note"></i> الوصف والملاحظات</h5>
                    </div>
                    <div class="card-body">
                        {% if daily_route.description %}
                            <h6>وصف الرحلة:</h6>
                            <p>{{ daily_route.description|linebreaks }}</p>
                        {% endif %}
                        
                        {% if daily_route.notes %}
                            <h6>ملاحظات:</h6>
                            <p>{{ daily_route.notes|linebreaks }}</p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
