<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:vehicle_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-truck"></i> بيانات المركبة</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle_type.id_for_label }}" class="form-label">{{ form.vehicle_type.label }}</label>
                                            {{ form.vehicle_type }}
                                            {% if form.vehicle_type.errors %}
                                                <div class="text-danger">{{ form.vehicle_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                            {{ form.status }}
                                            {% if form.status.errors %}
                                                <div class="text-danger">{{ form.status.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.plate_number.id_for_label }}" class="form-label">{{ form.plate_number.label }} <span class="text-danger">*</span></label>
                                            {{ form.plate_number }}
                                            {% if form.plate_number.errors %}
                                                <div class="text-danger">{{ form.plate_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.model.id_for_label }}" class="form-label">{{ form.model.label }} <span class="text-danger">*</span></label>
                                            {{ form.model }}
                                            {% if form.model.errors %}
                                                <div class="text-danger">{{ form.model.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.chassis_number.id_for_label }}" class="form-label">{{ form.chassis_number.label }}</label>
                                            {{ form.chassis_number }}
                                            {% if form.chassis_number.errors %}
                                                <div class="text-danger">{{ form.chassis_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.engine_number.id_for_label }}" class="form-label">{{ form.engine_number.label }}</label>
                                            {{ form.engine_number }}
                                            {% if form.engine_number.errors %}
                                                <div class="text-danger">{{ form.engine_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.fuel_type.id_for_label }}" class="form-label">{{ form.fuel_type.label }} <span class="text-danger">*</span></label>
                                        {{ form.fuel_type }}
                                        {% if form.fuel_type.errors %}
                                            <div class="text-danger">{{ form.fuel_type.errors }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.capacity.id_for_label }}" class="form-label">{{ form.capacity.label }}</label>
                                        {{ form.capacity }}
                                        {% if form.capacity.errors %}
                                            <div class="text-danger">{{ form.capacity.errors }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.last_maintenance.id_for_label }}" class="form-label">{{ form.last_maintenance.label }}</label>
                                            {{ form.last_maintenance }}
                                            {% if form.last_maintenance.errors %}
                                                <div class="text-danger">{{ form.last_maintenance.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.insurance_expiry.id_for_label }}" class="form-label">{{ form.insurance_expiry.label }}</label>
                                            {{ form.insurance_expiry }}
                                            {% if form.insurance_expiry.errors %}
                                                <div class="text-danger">{{ form.insurance_expiry.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{% url 'fleet:vehicle_list' %}" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> {{ submit_text }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle"></i> إرشادات</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> رقم اللوحة يجب أن يكون فريد</li>
                                    <li><i class="fas fa-check text-success"></i> رقم الشاسية ورقم الموتور يجب أن يكونا فريدين</li>
                                    <li><i class="fas fa-check text-success"></i> اختر نوع الوقود المناسب</li>
                                    <li><i class="fas fa-check text-success"></i> حدد السعة بوضوح (عدد الأشخاص أو الوزن)</li>
                                    <li><i class="fas fa-check text-success"></i> تواريخ الصيانة والتأمين اختيارية</li>
                                </ul>
                            </div>
                        </div>

                        {% if vehicle %}
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-history"></i> إجراءات إضافية</h6>
                            </div>
                            <div class="card-body">
                                <a href="{% url 'fleet:vehicle_detail' vehicle.pk %}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                {% if vehicle.current_driver %}
                                <a href="{% url 'fleet:driver_detail' vehicle.current_driver.pk %}" class="btn btn-outline-info btn-sm w-100">
                                    <i class="fas fa-user"></i> عرض السائق الحالي
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
