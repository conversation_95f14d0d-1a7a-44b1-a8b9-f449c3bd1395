<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عمليات التزود بالوقود - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">عمليات التزود بالوقود</h1>
                    <div>
                        <a href="{% url 'fleet:fuel_refill_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> تسجيل تزود جديد
                        </a>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">{{ total_refills }}</h5>
                                <p class="card-text">إجمالي العمليات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">{{ pending_refills }}</h5>
                                <p class="card-text">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">{{ approved_refills }}</h5>
                                <p class="card-text">موافق عليه</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">{{ rejected_refills }}</h5>
                                <p class="card-text">مرفوض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">{{ total_quantity|floatformat:0 }}</h5>
                                <p class="card-text">إجمالي اللترات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-dark">{{ total_cost|floatformat:0 }}</h5>
                                <p class="card-text">إجمالي التكلفة (ريال)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الشهر الحالي -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-calendar-month"></i> إحصائيات الشهر الحالي</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h5 class="text-primary">{{ this_month_quantity|floatformat:0 }} لتر</h5>
                                        <p class="text-muted">كمية الوقود</p>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success">{{ this_month_cost|floatformat:0 }} ريال</h5>
                                        <p class="text-muted">التكلفة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول عمليات التزود -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-gas-pump"></i> عمليات التزود بالوقود</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المركبة</th>
                                        <th>السائق</th>
                                        <th>التاريخ</th>
                                        <th>نوع الوقود</th>
                                        <th>الكمية</th>
                                        <th>السعر/لتر</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>صورة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for refill in fuel_refills %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'fleet:vehicle_detail' refill.vehicle.pk %}">
                                                {{ refill.vehicle.plate_number }}
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ refill.vehicle.model }}</small>
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:driver_detail' refill.driver.pk %}">
                                                {{ refill.driver.full_name }}
                                            </a>
                                        </td>
                                        <td>{{ refill.refill_date|date:"Y-m-d H:i" }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ refill.get_fuel_type_display }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ refill.quantity }} لتر</strong>
                                            <br>
                                            <small class="text-muted">{{ refill.fuel_efficiency_estimate }}</small>
                                        </td>
                                        <td>{{ refill.price_per_liter }} ريال</td>
                                        <td>
                                            <strong>{{ refill.total_amount }} ريال</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ refill.payment_display }}</span>
                                        </td>
                                        <td>
                                            {% if refill.status == 'pending' %}
                                                <span class="badge bg-warning">في الانتظار</span>
                                            {% elif refill.status == 'approved' %}
                                                <span class="badge bg-success">موافق عليه</span>
                                            {% elif refill.status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوض</span>
                                            {% else %}
                                                <span class="badge bg-info">قيد المراجعة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if refill.pump_receipt_image %}
                                                <i class="fas fa-camera text-success" title="يوجد صورة"></i>
                                            {% else %}
                                                <i class="fas fa-camera text-muted" title="لا توجد صورة"></i>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:fuel_refill_detail' refill.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            {% if refill.status in 'pending,under_review' %}
                                                <a href="{% url 'fleet:fuel_refill_approve' refill.pk %}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i> موافقة
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-gas-pump fa-3x mb-3"></i>
                                                <h5>لا توجد عمليات تزود مسجلة</h5>
                                                <p>ابدأ بتسجيل عملية التزود الأولى</p>
                                                <a href="{% url 'fleet:fuel_refill_create' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> تسجيل تزود جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
