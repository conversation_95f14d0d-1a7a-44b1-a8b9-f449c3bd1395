from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from fleet.models import Company, UserProfile


class Command(BaseCommand):
    help = 'Create UserProfile for users who do not have one'

    def handle(self, *args, **options):
        # Create default company if it doesn't exist
        default_company, created = Company.objects.get_or_create(
            name="شركة افتراضية",
            defaults={
                'address': 'عنوان افتراضي',
                'phone': '************',
                'activity': 'نشاط افتراضي'
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created default company: {default_company.name}')
            )

        # Find users without profiles
        users_without_profiles = User.objects.filter(profile__isnull=True)
        
        created_profiles = 0
        for user in users_without_profiles:
            UserProfile.objects.create(
                user=user,
                company=default_company,
                role='manager'  # Default role
            )
            created_profiles += 1
            self.stdout.write(
                self.style.SUCCESS(f'Created profile for user: {user.username}')
            )
        
        if created_profiles == 0:
            self.stdout.write(
                self.style.WARNING('No users found without profiles.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created {created_profiles} user profiles.')
            )
