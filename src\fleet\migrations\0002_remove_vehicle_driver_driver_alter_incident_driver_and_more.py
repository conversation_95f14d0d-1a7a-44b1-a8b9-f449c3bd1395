# Generated by Django 5.2.3 on 2025-06-10 11:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fleet', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='vehicle',
            name='driver',
        ),
        migrations.CreateModel(
            name='Driver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(max_length=50, verbose_name='اسم العائلة')),
                ('national_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('birth_date', models.DateField(verbose_name='تاريخ الميلاد')),
                ('license_number', models.CharField(max_length=30, unique=True, verbose_name='رقم الرخصة')),
                ('license_type', models.CharField(choices=[('private', 'رخصة خاصة'), ('public', 'رخصة عامة'), ('heavy', 'رخصة ثقيلة'), ('motorcycle', 'رخصة دراجة نارية')], max_length=20, verbose_name='نوع الرخصة')),
                ('license_issue_date', models.DateField(verbose_name='تاريخ إصدار الرخصة')),
                ('license_expiry_date', models.DateField(verbose_name='تاريخ انتهاء الرخصة')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الراتب')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('suspended', 'موقوف'), ('on_leave', 'في إجازة')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drivers', to='fleet.company')),
                ('user_profile', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='driver_info', to='fleet.userprofile')),
            ],
            options={
                'verbose_name': 'سائق',
                'verbose_name_plural': 'السائقين',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.AlterField(
            model_name='incident',
            name='driver',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reported_incidents', to='fleet.driver'),
        ),
        migrations.AlterField(
            model_name='trip',
            name='driver',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trips', to='fleet.driver'),
        ),
        migrations.CreateModel(
            name='VehicleDriverAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField(verbose_name='تاريخ بدء الاستلام')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ نهاية الاستلام')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='البيان/الملاحظات')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='active', max_length=20, verbose_name='حالة التعيين')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_vehicles', to='fleet.userprofile', verbose_name='تم التعيين بواسطة')),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_assignments', to='fleet.driver', verbose_name='السائق')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='driver_assignments', to='fleet.vehicle', verbose_name='المركبة')),
            ],
            options={
                'verbose_name': 'تعيين سائق لمركبة',
                'verbose_name_plural': 'تعيينات السائقين للمركبات',
                'ordering': ['-start_date'],
                'constraints': [models.UniqueConstraint(condition=models.Q(('end_date__isnull', True)), fields=('vehicle',), name='unique_active_vehicle_assignment')],
            },
        ),
    ]
