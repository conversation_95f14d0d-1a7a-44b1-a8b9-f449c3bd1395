<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:odometer_reading_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- معلومات أساسية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle.id_for_label }}" class="form-label">{{ form.vehicle.label }} <span class="text-danger">*</span></label>
                                            {{ form.vehicle }}
                                            {% if form.vehicle.errors %}
                                                <div class="text-danger">{{ form.vehicle.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.driver.id_for_label }}" class="form-label">{{ form.driver.label }} <span class="text-danger">*</span></label>
                                            {{ form.driver }}
                                            {% if form.driver.errors %}
                                                <div class="text-danger">{{ form.driver.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.reading_date.id_for_label }}" class="form-label">{{ form.reading_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.reading_date }}
                                            {% if form.reading_date.errors %}
                                                <div class="text-danger">{{ form.reading_date.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.shift.id_for_label }}" class="form-label">{{ form.shift.label }}</label>
                                            {{ form.shift }}
                                            {% if form.shift.errors %}
                                                <div class="text-danger">{{ form.shift.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- قراءة العداد -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-tachometer-alt"></i> قراءة العداد</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>مهم:</strong> تأكد من دقة قراءة العداد وأن الرقم أكبر من آخر قراءة مسجلة.
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.odometer_reading.id_for_label }}" class="form-label">{{ form.odometer_reading.label }} <span class="text-danger">*</span></label>
                                        {{ form.odometer_reading }}
                                        {% if form.odometer_reading.errors %}
                                            <div class="text-danger">{{ form.odometer_reading.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">أدخل قراءة العداد بالكيلومتر كما تظهر في المركبة</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.odometer_image.id_for_label }}" class="form-label">{{ form.odometer_image.label }}</label>
                                        {{ form.odometer_image }}
                                        {% if form.odometer_image.errors %}
                                            <div class="text-danger">{{ form.odometer_image.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">التقط صورة واضحة لعداد الكيلومتر (اختياري لكن مستحسن)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- أوقات الوردية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock"></i> أوقات الوردية (اختياري)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.start_time.id_for_label }}" class="form-label">{{ form.start_time.label }}</label>
                                            {{ form.start_time }}
                                            {% if form.start_time.errors %}
                                                <div class="text-danger">{{ form.start_time.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.end_time.id_for_label }}" class="form-label">{{ form.end_time.label }}</label>
                                            {{ form.end_time }}
                                            {% if form.end_time.errors %}
                                                <div class="text-danger">{{ form.end_time.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-plus-circle"></i> معلومات إضافية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.fuel_level.id_for_label }}" class="form-label">{{ form.fuel_level.label }}</label>
                                            {{ form.fuel_level }}
                                            {% if form.fuel_level.errors %}
                                                <div class="text-danger">{{ form.fuel_level.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.location.id_for_label }}" class="form-label">{{ form.location.label }}</label>
                                            {{ form.location }}
                                            {% if form.location.errors %}
                                                <div class="text-danger">{{ form.location.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- عرض أخطاء النموذج العامة -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'fleet:odometer_reading_list' %}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ submit_text }}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> إرشادات التسجيل</h6>
                                </div>
                                <div class="card-body">
                                    <h6>خطوات التسجيل:</h6>
                                    <ol class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> 1. اختر المركبة والسائق</li>
                                        <li><i class="fas fa-check text-success"></i> 2. حدد تاريخ القراءة</li>
                                        <li><i class="fas fa-check text-success"></i> 3. أدخل قراءة العداد بدقة</li>
                                        <li><i class="fas fa-check text-success"></i> 4. التقط صورة للعداد</li>
                                        <li><i class="fas fa-check text-success"></i> 5. أضف معلومات إضافية</li>
                                    </ol>

                                    <h6 class="mt-3">نصائح مهمة:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-lightbulb text-warning"></i> تأكد من وضوح الصورة</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> سجل القراءة يومياً</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> تحقق من صحة الرقم</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> أضف ملاحظات مفيدة</li>
                                    </ul>

                                    <h6 class="mt-3">الورديات:</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-warning">الصباح</span> 6:00 - 14:00</li>
                                        <li><span class="badge bg-info">المساء</span> 14:00 - 22:00</li>
                                        <li><span class="badge bg-dark">الليل</span> 22:00 - 6:00</li>
                                        <li><span class="badge bg-primary">يوم كامل</span> 24 ساعة</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-camera"></i> نصائح التصوير</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> تأكد من وضوح الأرقام</li>
                                        <li><i class="fas fa-check text-success"></i> استخدم إضاءة جيدة</li>
                                        <li><i class="fas fa-check text-success"></i> تجنب الانعكاسات</li>
                                        <li><i class="fas fa-check text-success"></i> اقترب من العداد</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الصورة قبل الرفع
        document.addEventListener('DOMContentLoaded', function() {
            const imageInput = document.getElementById('{{ form.odometer_image.id_for_label }}');
            
            if (imageInput) {
                imageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            // إنشاء معاينة للصورة
                            let preview = document.getElementById('image-preview');
                            if (!preview) {
                                preview = document.createElement('div');
                                preview.id = 'image-preview';
                                preview.className = 'mt-2';
                                imageInput.parentNode.appendChild(preview);
                            }
                            
                            preview.innerHTML = `
                                <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                <p class="text-muted mt-1">معاينة الصورة</p>
                            `;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>
</body>
</html>
