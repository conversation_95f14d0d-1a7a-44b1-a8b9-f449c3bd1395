<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إكمال الرحلة - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إكمال الرحلة: {{ daily_route.route_name }}</h1>
                    <div>
                        <a href="{% url 'fleet:daily_route_detail' daily_route.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <!-- ملخص الرحلة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> ملخص الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>المركبة:</strong> {{ daily_route.vehicle.plate_number }} - {{ daily_route.vehicle.model }}</p>
                                        <p><strong>السائق:</strong> {{ daily_route.driver.full_name }}</p>
                                        <p><strong>تاريخ الرحلة:</strong> {{ daily_route.route_date|date:"Y-m-d" }}</p>
                                        <p><strong>بدأت في:</strong> {{ daily_route.actual_start_time }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>نوع الرحلة:</strong> 
                                            <span class="badge bg-info">{{ daily_route.get_route_type_display }}</span>
                                        </p>
                                        <p><strong>الأولوية:</strong> 
                                            <span class="badge bg-{{ daily_route.priority_color }}">{{ daily_route.get_priority_display }}</span>
                                        </p>
                                        <p><strong>الوقت المخطط للانتهاء:</strong> {{ daily_route.planned_end_time }}</p>
                                        {% if daily_route.start_odometer %}
                                            <p><strong>قراءة العداد عند البداية:</strong> {{ daily_route.start_odometer }} كم</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نموذج إكمال الرحلة -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-check"></i> إكمال الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <strong>تهانينا!</strong> أنت على وشك إكمال الرحلة. يرجى تعبئة البيانات التالية.
                                </div>

                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="end_odometer" class="form-label">قراءة العداد عند النهاية <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="end_odometer" name="end_odometer" 
                                                   placeholder="123456" min="{% if daily_route.start_odometer %}{{ daily_route.start_odometer }}{% else %}0{% endif %}" required>
                                            <div class="form-text">أدخل قراءة العداد الحالية للمركبة</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="actual_cost" class="form-label">التكلفة الفعلية (ريال)</label>
                                            <input type="number" class="form-control" id="actual_cost" name="actual_cost" 
                                                   placeholder="1500.00" step="0.01" min="0">
                                            <div class="form-text">
                                                التكلفة المقدرة: 
                                                {% if daily_route.estimated_cost %}
                                                    {{ daily_route.estimated_cost }} ريال
                                                {% else %}
                                                    غير محددة
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات الرحلة</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                                  placeholder="أضف أي ملاحظات حول الرحلة، المشاكل التي واجهتها، أو أي معلومات مهمة..."></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{% url 'fleet:daily_route_detail' daily_route.pk %}" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-check"></i> إكمال الرحلة
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- إحصائيات الرحلة -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-line"></i> إحصائيات الرحلة</h6>
                            </div>
                            <div class="card-body">
                                {% if daily_route.estimated_distance %}
                                    <p><strong>المسافة المقدرة:</strong> {{ daily_route.estimated_distance }} كم</p>
                                {% endif %}
                                
                                {% if daily_route.planned_duration %}
                                    <p><strong>المدة المخططة:</strong> {{ daily_route.planned_duration }}</p>
                                {% endif %}
                                
                                {% if daily_route.actual_duration %}
                                    <p><strong>المدة الفعلية حتى الآن:</strong> {{ daily_route.actual_duration }}</p>
                                {% endif %}
                                
                                {% if daily_route.is_delayed %}
                                    <p><strong>التأخير:</strong> 
                                        <span class="text-warning">{{ daily_route.delay_duration }}</span>
                                    </p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- قائمة التحقق النهائية -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-clipboard-check"></i> قائمة التحقق النهائية</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="final_check1">
                                    <label class="form-check-label" for="final_check1">
                                        تم تسليم جميع البضائع/الخدمات
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="final_check2">
                                    <label class="form-check-label" for="final_check2">
                                        تم الحصول على توقيع العميل
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="final_check3">
                                    <label class="form-check-label" for="final_check3">
                                        تم فحص المركبة بعد الرحلة
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="final_check4">
                                    <label class="form-check-label" for="final_check4">
                                        تم تنظيف المركبة
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="final_check5">
                                    <label class="form-check-label" for="final_check5">
                                        تم إرجاع جميع الوثائق
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات مهمة -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> تأكد من دقة قراءة العداد</li>
                                    <li><i class="fas fa-check text-success"></i> احسب التكلفة الفعلية بدقة</li>
                                    <li><i class="fas fa-check text-success"></i> أضف ملاحظات مفيدة للمستقبل</li>
                                    <li><i class="fas fa-check text-success"></i> تأكد من عدم وجود أضرار</li>
                                </ul>
                            </div>
                        </div>

                        <!-- تقييم الأداء المتوقع -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-star"></i> تقييم الأداء المتوقع</h6>
                            </div>
                            <div class="card-body">
                                <div id="performance-preview">
                                    <p class="text-muted">سيتم حساب تقييم الأداء بعد إكمال الرحلة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const endOdometerInput = document.getElementById('end_odometer');
            const actualCostInput = document.getElementById('actual_cost');
            const performancePreview = document.getElementById('performance-preview');
            const submitButton = document.querySelector('button[type="submit"]');
            const checkboxes = document.querySelectorAll('.form-check-input');
            
            // حساب المسافة المقطوعة
            function calculateDistance() {
                const startOdometer = {{ daily_route.start_odometer|default:0 }};
                const endOdometer = parseInt(endOdometerInput.value) || 0;
                
                if (endOdometer > startOdometer) {
                    const distance = endOdometer - startOdometer;
                    return distance;
                }
                return 0;
            }
            
            // تحديث معاينة الأداء
            function updatePerformancePreview() {
                const distance = calculateDistance();
                const estimatedDistance = {{ daily_route.estimated_distance|default:0 }};
                const actualCost = parseFloat(actualCostInput.value) || 0;
                const estimatedCost = {{ daily_route.estimated_cost|default:0 }};
                
                let preview = '<h6>معاينة الأداء:</h6>';
                
                if (distance > 0) {
                    preview += `<p><strong>المسافة المقطوعة:</strong> ${distance} كم</p>`;
                    
                    if (estimatedDistance > 0) {
                        const distanceDiff = distance - estimatedDistance;
                        if (Math.abs(distanceDiff) <= 10) {
                            preview += '<p><span class="badge bg-success">مسافة ممتازة</span></p>';
                        } else if (distanceDiff > 10) {
                            preview += '<p><span class="badge bg-warning">مسافة أكثر من المتوقع</span></p>';
                        } else {
                            preview += '<p><span class="badge bg-info">مسافة أقل من المتوقع</span></p>';
                        }
                    }
                }
                
                if (actualCost > 0 && estimatedCost > 0) {
                    const costDiff = actualCost - estimatedCost;
                    if (Math.abs(costDiff) <= estimatedCost * 0.1) {
                        preview += '<p><span class="badge bg-success">تكلفة ممتازة</span></p>';
                    } else if (costDiff > 0) {
                        preview += '<p><span class="badge bg-warning">تكلفة أعلى من المتوقع</span></p>';
                    } else {
                        preview += '<p><span class="badge bg-info">تكلفة أقل من المتوقع</span></p>';
                    }
                }
                
                performancePreview.innerHTML = preview;
            }
            
            // تفعيل زر الإكمال عند اكتمال قائمة التحقق
            function updateSubmitButton() {
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                const hasEndOdometer = endOdometerInput.value.trim() !== '';
                
                submitButton.disabled = !(allChecked && hasEndOdometer);
            }
            
            // ربط الأحداث
            endOdometerInput.addEventListener('input', function() {
                updatePerformancePreview();
                updateSubmitButton();
            });
            
            actualCostInput.addEventListener('input', updatePerformancePreview);
            
            checkboxes.forEach(cb => {
                cb.addEventListener('change', updateSubmitButton);
            });
            
            // تحديث الحالة الأولية
            updateSubmitButton();
        });
    </script>
</body>
</html>
