<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:fuel_consumption_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- معلومات القياس الأساسية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-gas-pump"></i> معلومات القياس</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle.id_for_label }}" class="form-label">{{ form.vehicle.label }} <span class="text-danger">*</span></label>
                                            {{ form.vehicle }}
                                            {% if form.vehicle.errors %}
                                                <div class="text-danger">{{ form.vehicle.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.measurement_type.id_for_label }}" class="form-label">{{ form.measurement_type.label }}</label>
                                            {{ form.measurement_type }}
                                            {% if form.measurement_type.errors %}
                                                <div class="text-danger">{{ form.measurement_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.measurement_date.id_for_label }}" class="form-label">{{ form.measurement_date.label }} <span class="text-danger">*</span></label>
                                        {{ form.measurement_date }}
                                        {% if form.measurement_date.errors %}
                                            <div class="text-danger">{{ form.measurement_date.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات الاستهلاك -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-calculator"></i> بيانات الاستهلاك</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>ملاحظة:</strong> يمكنك إدخال معدل الاستهلاك مباشرة، أو إدخال المسافة وكمية الوقود وسيتم حساب المعدل تلقائياً.
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.consumption_rate.id_for_label }}" class="form-label">{{ form.consumption_rate.label }}</label>
                                            {{ form.consumption_rate }}
                                            {% if form.consumption_rate.errors %}
                                                <div class="text-danger">{{ form.consumption_rate.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.distance_traveled.id_for_label }}" class="form-label">{{ form.distance_traveled.label }}</label>
                                            {{ form.distance_traveled }}
                                            {% if form.distance_traveled.errors %}
                                                <div class="text-danger">{{ form.distance_traveled.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.fuel_amount.id_for_label }}" class="form-label">{{ form.fuel_amount.label }}</label>
                                            {{ form.fuel_amount }}
                                            {% if form.fuel_amount.errors %}
                                                <div class="text-danger">{{ form.fuel_amount.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.odometer_reading.id_for_label }}" class="form-label">{{ form.odometer_reading.label }}</label>
                                        {{ form.odometer_reading }}
                                        {% if form.odometer_reading.errors %}
                                            <div class="text-danger">{{ form.odometer_reading.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- فترة القياس -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-calendar"></i> فترة القياس (اختياري)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.period_start.id_for_label }}" class="form-label">{{ form.period_start.label }}</label>
                                            {{ form.period_start }}
                                            {% if form.period_start.errors %}
                                                <div class="text-danger">{{ form.period_start.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.period_end.id_for_label }}" class="form-label">{{ form.period_end.label }}</label>
                                            {{ form.period_end }}
                                            {% if form.period_end.errors %}
                                                <div class="text-danger">{{ form.period_end.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ظروف القياس -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-cloud"></i> ظروف القياس (اختياري)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.weather_condition.id_for_label }}" class="form-label">{{ form.weather_condition.label }}</label>
                                            {{ form.weather_condition }}
                                            {% if form.weather_condition.errors %}
                                                <div class="text-danger">{{ form.weather_condition.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.road_type.id_for_label }}" class="form-label">{{ form.road_type.label }}</label>
                                            {{ form.road_type }}
                                            {% if form.road_type.errors %}
                                                <div class="text-danger">{{ form.road_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.load_condition.id_for_label }}" class="form-label">{{ form.load_condition.label }}</label>
                                            {{ form.load_condition }}
                                            {% if form.load_condition.errors %}
                                                <div class="text-danger">{{ form.load_condition.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- عرض أخطاء النموذج العامة -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'fleet:fuel_consumption_list' %}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ submit_text }}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> إرشادات القياس</h6>
                                </div>
                                <div class="card-body">
                                    <h6>طرق حساب الاستهلاك:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> <strong>الطريقة الأولى:</strong> إدخال معدل الاستهلاك مباشرة</li>
                                        <li><i class="fas fa-check text-success"></i> <strong>الطريقة الثانية:</strong> إدخال المسافة وكمية الوقود</li>
                                    </ul>

                                    <h6 class="mt-3">تقييم الكفاءة:</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-success">ممتاز</span> أقل من 6 لتر/100كم</li>
                                        <li><span class="badge bg-info">جيد</span> 6-8 لتر/100كم</li>
                                        <li><span class="badge bg-warning">متوسط</span> 8-10 لتر/100كم</li>
                                        <li><span class="badge bg-danger">ضعيف</span> أكثر من 10 لتر/100كم</li>
                                    </ul>

                                    <h6 class="mt-3">نصائح:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-lightbulb text-warning"></i> سجل القياسات بانتظام</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> اذكر ظروف القياس للدقة</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> تأكد من صحة قراءة العداد</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين التاريخ الحالي كقيمة افتراضية
        document.addEventListener('DOMContentLoaded', function() {
            const measurementDateInput = document.getElementById('{{ form.measurement_date.id_for_label }}');
            if (measurementDateInput && !measurementDateInput.value) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                
                measurementDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
            }

            // حساب معدل الاستهلاك تلقائياً
            const distanceInput = document.getElementById('{{ form.distance_traveled.id_for_label }}');
            const fuelInput = document.getElementById('{{ form.fuel_amount.id_for_label }}');
            const consumptionInput = document.getElementById('{{ form.consumption_rate.id_for_label }}');

            function calculateConsumption() {
                const distance = parseFloat(distanceInput.value);
                const fuel = parseFloat(fuelInput.value);
                
                if (distance && fuel && distance > 0) {
                    const consumption = (fuel / distance) * 100;
                    consumptionInput.value = consumption.toFixed(2);
                }
            }

            if (distanceInput && fuelInput && consumptionInput) {
                distanceInput.addEventListener('input', calculateConsumption);
                fuelInput.addEventListener('input', calculateConsumption);
            }
        });
    </script>
</body>
</html>
