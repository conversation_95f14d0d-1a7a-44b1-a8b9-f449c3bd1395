<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:vehicle_assignments' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exchange-alt"></i> بيانات التعيين</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle.id_for_label }}" class="form-label">{{ form.vehicle.label }} <span class="text-danger">*</span></label>
                                            {{ form.vehicle }}
                                            {% if form.vehicle.errors %}
                                                <div class="text-danger">{{ form.vehicle.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">اختر المركبة التي تريد تعيين سائق لها</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.driver.id_for_label }}" class="form-label">{{ form.driver.label }} <span class="text-danger">*</span></label>
                                            {{ form.driver }}
                                            {% if form.driver.errors %}
                                                <div class="text-danger">{{ form.driver.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">اختر السائق المراد تعيينه</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }} <span class="text-danger">*</span></label>
                                        {{ form.start_date }}
                                        {% if form.start_date.errors %}
                                            <div class="text-danger">{{ form.start_date.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">تاريخ ووقت بدء استلام السائق للمركبة</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">أي ملاحظات أو تفاصيل إضافية حول التعيين</div>
                                    </div>

                                    <!-- عرض أخطاء النموذج العامة -->
                                    {% if form.non_field_errors %}
                                        <div class="alert alert-danger">
                                            {{ form.non_field_errors }}
                                        </div>
                                    {% endif %}

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{% url 'fleet:vehicle_assignments' %}" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> {{ submit_text }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>تنبيه:</strong> إذا كانت المركبة لديها تعيين نشط، سيتم إنهاؤه تلقائياً عند إنشاء التعيين الجديد.
                                </div>
                                
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> يمكن للمركبة الواحدة أن تكون لسائق واحد فقط في نفس الوقت</li>
                                    <li><i class="fas fa-check text-success"></i> السائق يجب أن يكون في حالة "نشط"</li>
                                    <li><i class="fas fa-check text-success"></i> يمكن إضافة ملاحظات لتوضيح سبب التعيين</li>
                                    <li><i class="fas fa-check text-success"></i> سيتم تسجيل من قام بالتعيين تلقائياً</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-link"></i> روابط سريعة</h6>
                            </div>
                            <div class="card-body">
                                <a href="{% url 'fleet:vehicle_create' %}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                                    <i class="fas fa-plus"></i> إضافة مركبة جديدة
                                </a>
                                <a href="{% url 'fleet:driver_create' %}" class="btn btn-outline-success btn-sm w-100 mb-2">
                                    <i class="fas fa-plus"></i> إضافة سائق جديد
                                </a>
                                <a href="{% url 'fleet:vehicle_assignments' %}" class="btn btn-outline-info btn-sm w-100">
                                    <i class="fas fa-list"></i> عرض جميع التعيينات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين التاريخ الحالي كقيمة افتراضية
        document.addEventListener('DOMContentLoaded', function() {
            const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
            if (startDateInput && !startDateInput.value) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                
                startDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
            }
        });
    </script>
</body>
</html>
