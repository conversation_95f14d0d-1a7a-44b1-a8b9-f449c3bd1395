<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعيينات المركبات - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تعيينات المركبات</h1>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i> تعيين جديد
                    </button>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">التعيينات النشطة</h5>
                                <h2 class="text-success">{{ active_assignments.count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي التعيينات</h5>
                                <h2 class="text-primary">{{ assignments.count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">التعيينات المكتملة</h5>
                                <h2 class="text-info">{{ assignments.count|add:"-"|add:active_assignments.count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التعيينات النشطة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clock text-success"></i> التعيينات النشطة</h5>
                    </div>
                    <div class="card-body">
                        {% if active_assignments %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>المركبة</th>
                                            <th>السائق</th>
                                            <th>تاريخ البداية</th>
                                            <th>المدة</th>
                                            <th>تم التعيين بواسطة</th>
                                            <th>البيان</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for assignment in active_assignments %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'fleet:vehicle_detail' assignment.vehicle.pk %}">
                                                    <strong>{{ assignment.vehicle.plate_number }}</strong>
                                                </a>
                                                <br>
                                                <small class="text-muted">{{ assignment.vehicle.model }}</small>
                                            </td>
                                            <td>
                                                <a href="{% url 'fleet:driver_detail' assignment.driver.pk %}">
                                                    <strong>{{ assignment.driver.full_name }}</strong>
                                                </a>
                                                <br>
                                                <small class="text-muted">{{ assignment.driver.license_number }}</small>
                                            </td>
                                            <td>{{ assignment.start_date|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                <span class="badge bg-success">
                                                    {{ assignment.duration.days }} يوم
                                                </span>
                                            </td>
                                            <td>
                                                {% if assignment.assigned_by %}
                                                    {{ assignment.assigned_by.user.first_name }} {{ assignment.assigned_by.user.last_name }}
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </td>
                                            <td>{{ assignment.notes|default:"لا يوجد"|truncatewords:5 }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-danger" title="إنهاء التعيين">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <p>لا توجد تعيينات نشطة حالياً</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- جميع التعيينات -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> تاريخ جميع التعيينات</h5>
                    </div>
                    <div class="card-body">
                        {% if assignments %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المركبة</th>
                                            <th>السائق</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>المدة</th>
                                            <th>الحالة</th>
                                            <th>البيان</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for assignment in assignments %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'fleet:vehicle_detail' assignment.vehicle.pk %}">
                                                    {{ assignment.vehicle.plate_number }}
                                                </a>
                                            </td>
                                            <td>
                                                <a href="{% url 'fleet:driver_detail' assignment.driver.pk %}">
                                                    {{ assignment.driver.full_name }}
                                                </a>
                                            </td>
                                            <td>{{ assignment.start_date|date:"Y-m-d H:i" }}</td>
                                            <td>
                                                {% if assignment.end_date %}
                                                    {{ assignment.end_date|date:"Y-m-d H:i" }}
                                                {% else %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if assignment.end_date %}
                                                    {{ assignment.duration.days }} يوم
                                                {% else %}
                                                    {{ assignment.duration.days }} يوم (مستمر)
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if assignment.status == 'active' %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% elif assignment.status == 'completed' %}
                                                    <span class="badge bg-primary">مكتمل</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">ملغي</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ assignment.notes|default:"لا يوجد"|truncatewords:3 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-3x mb-3"></i>
                                <p>لا توجد تعيينات مسجلة</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
