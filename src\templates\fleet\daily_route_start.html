<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بدء الرحلة - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:daily_route_list' %}">
                                <i class="fas fa-route"></i> خطوط السير اليومية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">بدء الرحلة: {{ daily_route.route_name }}</h1>
                    <div>
                        <a href="{% url 'fleet:daily_route_detail' daily_route.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="row">
                    <div class="col-md-8">
                        <!-- معلومات الرحلة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> معلومات الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>المركبة:</strong> {{ daily_route.vehicle.plate_number }} - {{ daily_route.vehicle.model }}</p>
                                        <p><strong>السائق:</strong> {{ daily_route.driver.full_name }}</p>
                                        <p><strong>تاريخ الرحلة:</strong> {{ daily_route.route_date|date:"Y-m-d" }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>نوع الرحلة:</strong> 
                                            <span class="badge bg-info">{{ daily_route.get_route_type_display }}</span>
                                        </p>
                                        <p><strong>الأولوية:</strong> 
                                            <span class="badge bg-{{ daily_route.priority_color }}">{{ daily_route.get_priority_display }}</span>
                                        </p>
                                        <p><strong>الوقت المخطط للبداية:</strong> {{ daily_route.planned_start_time }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نقاط الرحلة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-map-marker-alt"></i> نقاط الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">نقطة البداية</h6>
                                        <p>{{ daily_route.start_location }}</p>
                                        {% if daily_route.start_latitude and daily_route.start_longitude %}
                                            <a href="https://maps.google.com/?q={{ daily_route.start_latitude }},{{ daily_route.start_longitude }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-map"></i> عرض في الخريطة
                                            </a>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-success">نقطة النهاية</h6>
                                        <p>{{ daily_route.end_location }}</p>
                                        {% if daily_route.end_latitude and daily_route.end_longitude %}
                                            <a href="https://maps.google.com/?q={{ daily_route.end_latitude }},{{ daily_route.end_longitude }}" target="_blank" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-map"></i> عرض في الخريطة
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نموذج بدء الرحلة -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-play"></i> بدء تنفيذ الرحلة</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>تنبيه:</strong> تأكد من فحص المركبة وجاهزيتها قبل بدء الرحلة.
                                </div>

                                <form method="post">
                                    {% csrf_token %}
                                    
                                    <div class="mb-3">
                                        <label for="start_odometer" class="form-label">قراءة العداد عند البداية (اختياري)</label>
                                        <input type="number" class="form-control" id="start_odometer" name="start_odometer" 
                                               placeholder="123456" min="0">
                                        <div class="form-text">أدخل قراءة العداد الحالية للمركبة</div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{% url 'fleet:daily_route_detail' daily_route.pk %}" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-play"></i> بدء الرحلة الآن
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- قائمة التحقق -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-clipboard-check"></i> قائمة التحقق قبل البدء</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check1">
                                    <label class="form-check-label" for="check1">
                                        فحص مستوى الوقود
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check2">
                                    <label class="form-check-label" for="check2">
                                        فحص ضغط الإطارات
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check3">
                                    <label class="form-check-label" for="check3">
                                        فحص الأضواء والإشارات
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check4">
                                    <label class="form-check-label" for="check4">
                                        فحص مستوى الزيت والماء
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check5">
                                    <label class="form-check-label" for="check5">
                                        التأكد من وجود الوثائق المطلوبة
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check6">
                                    <label class="form-check-label" for="check6">
                                        التأكد من نظافة المركبة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        {% if daily_route.client_name %}
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-phone"></i> معلومات الاتصال</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>العميل:</strong> {{ daily_route.client_name }}</p>
                                {% if daily_route.client_phone %}
                                    <p><strong>الهاتف:</strong> 
                                        <a href="tel:{{ daily_route.client_phone }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-phone"></i> {{ daily_route.client_phone }}
                                        </a>
                                    </p>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- نصائح السلامة -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-shield-alt"></i> نصائح السلامة</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> اربط حزام الأمان</li>
                                    <li><i class="fas fa-check text-success"></i> اتبع قوانين المرور</li>
                                    <li><i class="fas fa-check text-success"></i> حافظ على المسافة الآمنة</li>
                                    <li><i class="fas fa-check text-success"></i> تجنب استخدام الهاتف أثناء القيادة</li>
                                    <li><i class="fas fa-check text-success"></i> خذ استراحة كل ساعتين</li>
                                </ul>
                            </div>
                        </div>

                        <!-- أرقام الطوارئ -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6><i class="fas fa-exclamation-triangle"></i> أرقام الطوارئ</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>الطوارئ العامة:</strong> 
                                    <a href="tel:911" class="btn btn-sm btn-danger">911</a>
                                </p>
                                <p><strong>المرور:</strong> 
                                    <a href="tel:993" class="btn btn-sm btn-warning">993</a>
                                </p>
                                <p><strong>الدفاع المدني:</strong> 
                                    <a href="tel:998" class="btn btn-sm btn-info">998</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل زر البدء فقط عند اكتمال قائمة التحقق
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.form-check-input');
            const startButton = document.querySelector('button[type="submit"]');
            
            function updateButtonState() {
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                startButton.disabled = !allChecked;
                
                if (allChecked) {
                    startButton.classList.remove('btn-secondary');
                    startButton.classList.add('btn-success');
                } else {
                    startButton.classList.remove('btn-success');
                    startButton.classList.add('btn-secondary');
                }
            }
            
            checkboxes.forEach(cb => {
                cb.addEventListener('change', updateButtonState);
            });
            
            // تحديث الحالة الأولية
            updateButtonState();
        });
    </script>
</body>
</html>
