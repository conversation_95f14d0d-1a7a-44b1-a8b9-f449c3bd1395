<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{ title }}</h1>
                    <div>
                        <a href="{% url 'fleet:fuel_refill_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <!-- عرض الرسائل -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- معلومات أساسية -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.vehicle.id_for_label }}" class="form-label">{{ form.vehicle.label }} <span class="text-danger">*</span></label>
                                            {{ form.vehicle }}
                                            {% if form.vehicle.errors %}
                                                <div class="text-danger">{{ form.vehicle.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.driver.id_for_label }}" class="form-label">{{ form.driver.label }} <span class="text-danger">*</span></label>
                                            {{ form.driver }}
                                            {% if form.driver.errors %}
                                                <div class="text-danger">{{ form.driver.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.refill_date.id_for_label }}" class="form-label">{{ form.refill_date.label }} <span class="text-danger">*</span></label>
                                            {{ form.refill_date }}
                                            {% if form.refill_date.errors %}
                                                <div class="text-danger">{{ form.refill_date.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">يمكن التسجيل من 30 يوم في الماضي حتى 24 ساعة في المستقبل</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.fuel_type.id_for_label }}" class="form-label">{{ form.fuel_type.label }} <span class="text-danger">*</span></label>
                                            {{ form.fuel_type }}
                                            {% if form.fuel_type.errors %}
                                                <div class="text-danger">{{ form.fuel_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات التزود -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-gas-pump"></i> بيانات التزود</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>مهم:</strong> تأكد من دقة الكمية والسعر. سيتم حساب المبلغ الإجمالي تلقائياً.
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.quantity.id_for_label }}" class="form-label">{{ form.quantity.label }} <span class="text-danger">*</span></label>
                                            {{ form.quantity }}
                                            {% if form.quantity.errors %}
                                                <div class="text-danger">{{ form.quantity.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.price_per_liter.id_for_label }}" class="form-label">{{ form.price_per_liter.label }} <span class="text-danger">*</span></label>
                                            {{ form.price_per_liter }}
                                            {% if form.price_per_liter.errors %}
                                                <div class="text-danger">{{ form.price_per_liter.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.total_amount.id_for_label }}" class="form-label">{{ form.total_amount.label }}</label>
                                            {{ form.total_amount }}
                                            {% if form.total_amount.errors %}
                                                <div class="text-danger">{{ form.total_amount.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">سيتم الحساب تلقائياً</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.odometer_reading.id_for_label }}" class="form-label">{{ form.odometer_reading.label }} <span class="text-danger">*</span></label>
                                        {{ form.odometer_reading }}
                                        {% if form.odometer_reading.errors %}
                                            <div class="text-danger">{{ form.odometer_reading.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">أدخل قراءة العداد وقت التزود</div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات المحطة -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-map-marker-alt"></i> معلومات المحطة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.station_name.id_for_label }}" class="form-label">{{ form.station_name.label }}</label>
                                            {{ form.station_name }}
                                            {% if form.station_name.errors %}
                                                <div class="text-danger">{{ form.station_name.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.pump_number.id_for_label }}" class="form-label">{{ form.pump_number.label }}</label>
                                            {{ form.pump_number }}
                                            {% if form.pump_number.errors %}
                                                <div class="text-danger">{{ form.pump_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.station_location.id_for_label }}" class="form-label">{{ form.station_location.label }}</label>
                                        {{ form.station_location }}
                                        {% if form.station_location.errors %}
                                            <div class="text-danger">{{ form.station_location.errors }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.pump_receipt_image.id_for_label }}" class="form-label">{{ form.pump_receipt_image.label }}</label>
                                        {{ form.pump_receipt_image }}
                                        {% if form.pump_receipt_image.errors %}
                                            <div class="text-danger">{{ form.pump_receipt_image.errors }}</div>
                                        {% endif %}
                                        <div class="form-text">التقط صورة واضحة لإيصال المضخة (مستحسن)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات الدفع -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-credit-card"></i> بيانات الدفع</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.payment_method.id_for_label }}" class="form-label">{{ form.payment_method.label }} <span class="text-danger">*</span></label>
                                            {{ form.payment_method }}
                                            {% if form.payment_method.errors %}
                                                <div class="text-danger">{{ form.payment_method.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.card_last_four.id_for_label }}" class="form-label">{{ form.card_last_four.label }}</label>
                                            {{ form.card_last_four }}
                                            {% if form.card_last_four.errors %}
                                                <div class="text-danger">{{ form.card_last_four.errors }}</div>
                                            {% endif %}
                                            <div class="form-text">للبطاقات فقط</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.card_type.id_for_label }}" class="form-label">{{ form.card_type.label }}</label>
                                            {{ form.card_type }}
                                            {% if form.card_type.errors %}
                                                <div class="text-danger">{{ form.card_type.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.transaction_id.id_for_label }}" class="form-label">{{ form.transaction_id.label }}</label>
                                            {{ form.transaction_id }}
                                            {% if form.transaction_id.errors %}
                                                <div class="text-danger">{{ form.transaction_id.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.approval_code.id_for_label }}" class="form-label">{{ form.approval_code.label }}</label>
                                            {{ form.approval_code }}
                                            {% if form.approval_code.errors %}
                                                <div class="text-danger">{{ form.approval_code.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.receipt_number.id_for_label }}" class="form-label">{{ form.receipt_number.label }}</label>
                                            {{ form.receipt_number }}
                                            {% if form.receipt_number.errors %}
                                                <div class="text-danger">{{ form.receipt_number.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5><i class="fas fa-sticky-note"></i> ملاحظات</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="text-danger">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- عرض أخطاء النموذج العامة -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'fleet:fuel_refill_list' %}" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ submit_text }}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> إرشادات التسجيل</h6>
                                </div>
                                <div class="card-body">
                                    <h6>خطوات التسجيل:</h6>
                                    <ol class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> 1. اختر المركبة والسائق</li>
                                        <li><i class="fas fa-check text-success"></i> 2. حدد تاريخ ووقت التزود</li>
                                        <li><i class="fas fa-check text-success"></i> 3. اختر نوع الوقود</li>
                                        <li><i class="fas fa-check text-success"></i> 4. أدخل الكمية والسعر</li>
                                        <li><i class="fas fa-check text-success"></i> 5. سجل قراءة العداد</li>
                                        <li><i class="fas fa-check text-success"></i> 6. أضف معلومات المحطة</li>
                                        <li><i class="fas fa-check text-success"></i> 7. التقط صورة الإيصال</li>
                                        <li><i class="fas fa-check text-success"></i> 8. أدخل بيانات الدفع</li>
                                    </ol>

                                    <h6 class="mt-3">نصائح مهمة:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-lightbulb text-warning"></i> تأكد من وضوح الصورة</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> احفظ الإيصال الأصلي</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> تحقق من صحة البيانات</li>
                                        <li><i class="fas fa-lightbulb text-warning"></i> أدخل قراءة العداد بدقة</li>
                                    </ul>

                                    <h6 class="mt-3">قيود التاريخ والوقت:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-clock text-info"></i> يمكن التسجيل حتى 30 يوم في الماضي</li>
                                        <li><i class="fas fa-clock text-info"></i> يمكن التسجيل حتى 24 ساعة في المستقبل</li>
                                        <li><i class="fas fa-clock text-info"></i> فترة ساعة على الأقل بين عمليات التزود</li>
                                        <li><i class="fas fa-clock text-info"></i> مراعاة فروق التوقيت والتأخير</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6><i class="fas fa-calculator"></i> حاسبة التكلفة</h6>
                                </div>
                                <div class="card-body">
                                    <div id="cost-calculator">
                                        <p class="text-muted">سيتم حساب المبلغ الإجمالي تلقائياً عند إدخال الكمية والسعر</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحديد الحد الأدنى والأقصى لتاريخ التزود
            const refillDateInput = document.getElementById('{{ form.refill_date.id_for_label }}');
            if (refillDateInput) {
                const now = new Date();

                // الحد الأدنى: 30 يوم في الماضي
                const minDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

                // الحد الأقصى: 24 ساعة في المستقبل
                const maxDate = new Date(now.getTime() + (24 * 60 * 60 * 1000));

                // تنسيق التاريخ للـ datetime-local input
                const formatDateTime = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    return `${year}-${month}-${day}T${hours}:${minutes}`;
                };

                refillDateInput.min = formatDateTime(minDate);
                refillDateInput.max = formatDateTime(maxDate);

                // تعيين القيمة الافتراضية للوقت الحالي إذا لم تكن محددة
                if (!refillDateInput.value) {
                    refillDateInput.value = formatDateTime(now);
                }

                // إضافة تحقق إضافي عند تغيير التاريخ
                refillDateInput.addEventListener('change', function() {
                    const selectedDate = new Date(this.value);
                    const now = new Date();

                    if (selectedDate > maxDate) {
                        alert('لا يمكن اختيار تاريخ أكثر من 24 ساعة في المستقبل');
                        this.value = formatDateTime(now);
                    } else if (selectedDate < minDate) {
                        alert('لا يمكن اختيار تاريخ أكثر من 30 يوم في الماضي');
                        this.value = formatDateTime(now);
                    }
                });
            }
            // حساب المبلغ الإجمالي تلقائياً
            const quantityInput = document.getElementById('{{ form.quantity.id_for_label }}');
            const priceInput = document.getElementById('{{ form.price_per_liter.id_for_label }}');
            const totalInput = document.getElementById('{{ form.total_amount.id_for_label }}');
            const calculator = document.getElementById('cost-calculator');

            function calculateTotal() {
                const quantity = parseFloat(quantityInput.value) || 0;
                const price = parseFloat(priceInput.value) || 0;
                const total = quantity * price;
                
                if (total > 0) {
                    totalInput.value = total.toFixed(2);
                    calculator.innerHTML = `
                        <div class="alert alert-success">
                            <strong>الحساب:</strong><br>
                            ${quantity} لتر × ${price} ريال = <strong>${total.toFixed(2)} ريال</strong>
                        </div>
                    `;
                } else {
                    totalInput.value = '';
                    calculator.innerHTML = '<p class="text-muted">سيتم حساب المبلغ الإجمالي تلقائياً عند إدخال الكمية والسعر</p>';
                }
            }

            quantityInput.addEventListener('input', calculateTotal);
            priceInput.addEventListener('input', calculateTotal);

            // معاينة الصورة
            const imageInput = document.getElementById('{{ form.pump_receipt_image.id_for_label }}');
            
            if (imageInput) {
                imageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            let preview = document.getElementById('image-preview');
                            if (!preview) {
                                preview = document.createElement('div');
                                preview.id = 'image-preview';
                                preview.className = 'mt-2';
                                imageInput.parentNode.appendChild(preview);
                            }
                            
                            preview.innerHTML = `
                                <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                <p class="text-muted mt-1">معاينة الصورة</p>
                            `;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // إظهار/إخفاء حقول البطاقة حسب طريقة الدفع
            const paymentMethodSelect = document.getElementById('{{ form.payment_method.id_for_label }}');
            const cardFields = ['{{ form.card_last_four.id_for_label }}', '{{ form.card_type.id_for_label }}', '{{ form.transaction_id.id_for_label }}', '{{ form.approval_code.id_for_label }}'];
            
            function toggleCardFields() {
                const paymentMethod = paymentMethodSelect.value;
                const showCardFields = ['visa', 'mastercard', 'company_card', 'fuel_card'].includes(paymentMethod);
                
                cardFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.closest('.mb-3').style.display = showCardFields ? 'block' : 'none';
                    }
                });
            }

            paymentMethodSelect.addEventListener('change', toggleCardFields);
            toggleCardFields(); // تشغيل عند التحميل
        });
    </script>
</body>
</html>
