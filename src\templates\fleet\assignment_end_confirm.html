<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد إنهاء التعيين - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تأكيد إنهاء التعيين</h1>
                    <div>
                        <a href="{% url 'fleet:vehicle_assignments' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5><i class="fas fa-exclamation-triangle"></i> تأكيد إنهاء التعيين</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>تنبيه:</strong> هذا الإجراء سيؤدي إلى إنهاء التعيين النشط وتسجيل تاريخ النهاية.
                                </div>

                                <h6>تفاصيل التعيين المراد إنهاؤه:</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>المركبة:</strong></td>
                                        <td>
                                            <a href="{% url 'fleet:vehicle_detail' assignment.vehicle.pk %}">
                                                {{ assignment.vehicle.plate_number }} - {{ assignment.vehicle.model }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>السائق:</strong></td>
                                        <td>
                                            <a href="{% url 'fleet:driver_detail' assignment.driver.pk %}">
                                                {{ assignment.driver.full_name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ بدء التعيين:</strong></td>
                                        <td>{{ assignment.start_date|date:"Y-m-d H:i" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مدة التعيين حتى الآن:</strong></td>
                                        <td>{{ assignment.duration.days }} يوم</td>
                                    </tr>
                                    <tr>
                                        <td><strong>البيان الحالي:</strong></td>
                                        <td>{{ assignment.notes|default:"لا يوجد" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تم التعيين بواسطة:</strong></td>
                                        <td>
                                            {% if assignment.assigned_by %}
                                                {{ assignment.assigned_by.user.first_name }} {{ assignment.assigned_by.user.last_name }}
                                            {% else %}
                                                غير محدد
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>

                                <hr>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ما سيحدث عند التأكيد:</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success"></i> سيتم تسجيل تاريخ ووقت الإنهاء</li>
                                            <li><i class="fas fa-check text-success"></i> ستتغير حالة التعيين إلى "مكتمل"</li>
                                            <li><i class="fas fa-check text-success"></i> ستصبح المركبة متاحة لتعيين سائق آخر</li>
                                            <li><i class="fas fa-check text-success"></i> سيبقى السجل محفوظ في التاريخ</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <i class="fas fa-lightbulb"></i>
                                            <strong>نصيحة:</strong> يمكنك إضافة ملاحظة حول سبب إنهاء التعيين من خلال تعديل التعيين في لوحة الإدارة.
                                        </div>
                                    </div>
                                </div>

                                <form method="post" class="mt-4">
                                    {% csrf_token %}
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <a href="{% url 'fleet:vehicle_assignments' %}" class="btn btn-secondary me-md-2">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-stop"></i> تأكيد إنهاء التعيين
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
