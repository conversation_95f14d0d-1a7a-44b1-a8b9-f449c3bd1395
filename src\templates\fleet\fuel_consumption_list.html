<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قياسات استهلاك الوقود - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">قياسات استهلاك الوقود</h1>
                    <div>
                        <a href="{% url 'fleet:fuel_consumption_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> تسجيل قياس جديد
                        </a>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">{{ total_measurements }}</h5>
                                <p class="card-text">إجمالي القياسات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    {% if avg_consumption %}{{ avg_consumption }} لتر/100كم{% else %}لا يوجد{% endif %}
                                </h5>
                                <p class="card-text">متوسط الاستهلاك</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    {% if best_consumption %}{{ best_consumption }} لتر/100كم{% else %}لا يوجد{% endif %}
                                </h5>
                                <p class="card-text">أفضل استهلاك</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">
                                    {% if worst_consumption %}{{ worst_consumption }} لتر/100كم{% else %}لا يوجد{% endif %}
                                </h5>
                                <p class="card-text">أسوأ استهلاك</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول القياسات -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-gas-pump"></i> قياسات استهلاك الوقود</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المركبة</th>
                                        <th>معدل الاستهلاك</th>
                                        <th>تاريخ المعايرة</th>
                                        <th>نوع القياس</th>
                                        <th>تقييم الكفاءة</th>
                                        <th>المسافة</th>
                                        <th>كمية الوقود</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for consumption in fuel_consumptions %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'fleet:vehicle_detail' consumption.vehicle.pk %}">
                                                {{ consumption.vehicle.plate_number }}
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ consumption.vehicle.model }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ consumption.consumption_rate }} لتر/100كم</strong>
                                        </td>
                                        <td>{{ consumption.measurement_date|date:"Y-m-d H:i" }}</td>
                                        <td>
                                            {% if consumption.measurement_type == 'manual' %}
                                                <span class="badge bg-primary">قياس يدوي</span>
                                            {% elif consumption.measurement_type == 'automatic' %}
                                                <span class="badge bg-success">قياس تلقائي</span>
                                            {% else %}
                                                <span class="badge bg-warning">تقدير</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ consumption.efficiency_color }}">
                                                {{ consumption.efficiency_rating }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if consumption.distance_traveled %}
                                                {{ consumption.distance_traveled }} كم
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if consumption.fuel_amount %}
                                                {{ consumption.fuel_amount }} لتر
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:fuel_consumption_detail' consumption.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="{% url 'fleet:vehicle_fuel_history' consumption.vehicle.pk %}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-history"></i> التاريخ
                                            </a>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-gas-pump fa-3x mb-3"></i>
                                                <h5>لا توجد قياسات مسجلة</h5>
                                                <p>ابدأ بتسجيل قياس استهلاك الوقود الأول</p>
                                                <a href="{% url 'fleet:fuel_consumption_create' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> تسجيل قياس جديد
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
