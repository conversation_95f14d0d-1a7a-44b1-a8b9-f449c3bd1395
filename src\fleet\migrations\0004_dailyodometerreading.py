# Generated by Django 5.2.3 on 2025-06-10 12:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fleet', '0003_vehicle_chassis_number_vehicle_engine_number_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyOdometerReading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reading_date', models.DateField(verbose_name='تاريخ القراءة')),
                ('odometer_reading', models.PositiveIntegerField(verbose_name='قراءة العداد (كم)')),
                ('previous_reading', models.PositiveIntegerField(blank=True, null=True, verbose_name='القراءة السابقة (كم)')),
                ('daily_distance', models.PositiveIntegerField(blank=True, null=True, verbose_name='المسافة اليومية (كم)')),
                ('shift', models.CharField(choices=[('morning', 'الصباح'), ('evening', 'المساء'), ('night', 'الليل'), ('full_day', 'يوم كامل')], default='full_day', max_length=20, verbose_name='الوردية')),
                ('start_time', models.TimeField(blank=True, null=True, verbose_name='وقت البداية')),
                ('end_time', models.TimeField(blank=True, null=True, verbose_name='وقت النهاية')),
                ('odometer_image', models.ImageField(blank=True, null=True, upload_to='odometer_readings/%Y/%m/%d/', verbose_name='صورة العداد')),
                ('fuel_level', models.CharField(blank=True, max_length=20, null=True, verbose_name='مستوى الوقود')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='الموقع')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('verified', 'تم التحقق'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='حالة التسجيل')),
                ('verification_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التحقق')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='سبب الرفض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='odometer_readings', to='fleet.driver', verbose_name='السائق')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='odometer_readings', to='fleet.vehicle', verbose_name='المركبة')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_readings', to='fleet.userprofile', verbose_name='تم التحقق بواسطة')),
            ],
            options={
                'verbose_name': 'قراءة عداد يومية',
                'verbose_name_plural': 'قراءات العداد اليومية',
                'ordering': ['-reading_date', '-created_at'],
                'indexes': [models.Index(fields=['vehicle', '-reading_date'], name='fleet_daily_vehicle_78af58_idx'), models.Index(fields=['driver', '-reading_date'], name='fleet_daily_driver__fbc980_idx'), models.Index(fields=['reading_date'], name='fleet_daily_reading_158fc0_idx'), models.Index(fields=['status'], name='fleet_daily_status_251871_idx')],
                'unique_together': {('vehicle', 'driver', 'reading_date', 'shift')},
            },
        ),
    ]
