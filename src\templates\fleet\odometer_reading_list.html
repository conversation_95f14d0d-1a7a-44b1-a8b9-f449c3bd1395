<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قراءات العداد اليومية - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:driver_list' %}">
                                <i class="fas fa-users"></i> السائقين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_assignments' %}">
                                <i class="fas fa-exchange-alt"></i> تعيينات المركبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_consumption_list' %}">
                                <i class="fas fa-gas-pump"></i> استهلاك الوقود
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'fleet:odometer_reading_list' %}">
                                <i class="fas fa-tachometer-alt"></i> قراءات العداد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:fuel_refill_list' %}">
                                <i class="fas fa-gas-pump"></i> التزود بالوقود
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">قراءات العداد اليومية</h1>
                    <div>
                        <a href="{% url 'fleet:odometer_reading_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> تسجيل قراءة جديدة
                        </a>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">{{ total_readings }}</h5>
                                <p class="card-text">إجمالي القراءات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">{{ pending_readings }}</h5>
                                <p class="card-text">في الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">{{ verified_readings }}</h5>
                                <p class="card-text">تم التحقق</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">{{ rejected_readings }}</h5>
                                <p class="card-text">مرفوض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">{{ today_readings }}</h5>
                                <p class="card-text">قراءات اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول القراءات -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tachometer-alt"></i> قراءات العداد</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المركبة</th>
                                        <th>السائق</th>
                                        <th>التاريخ</th>
                                        <th>قراءة العداد</th>
                                        <th>المسافة اليومية</th>
                                        <th>الوردية</th>
                                        <th>الحالة</th>
                                        <th>صورة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reading in odometer_readings %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'fleet:vehicle_detail' reading.vehicle.pk %}">
                                                {{ reading.vehicle.plate_number }}
                                            </a>
                                            <br>
                                            <small class="text-muted">{{ reading.vehicle.model }}</small>
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:driver_detail' reading.driver.pk %}">
                                                {{ reading.driver.full_name }}
                                            </a>
                                        </td>
                                        <td>{{ reading.reading_date|date:"Y-m-d" }}</td>
                                        <td>
                                            <strong>{{ reading.odometer_reading|floatformat:0 }} كم</strong>
                                        </td>
                                        <td>
                                            {% if reading.daily_distance %}
                                                <span class="badge bg-{{ reading.distance_color }}">
                                                    {{ reading.daily_distance }} كم
                                                </span>
                                                <br>
                                                <small class="text-muted">{{ reading.distance_status }}</small>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if reading.shift == 'morning' %}
                                                <span class="badge bg-warning">الصباح</span>
                                            {% elif reading.shift == 'evening' %}
                                                <span class="badge bg-info">المساء</span>
                                            {% elif reading.shift == 'night' %}
                                                <span class="badge bg-dark">الليل</span>
                                            {% else %}
                                                <span class="badge bg-primary">يوم كامل</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if reading.status == 'pending' %}
                                                <span class="badge bg-warning">في الانتظار</span>
                                            {% elif reading.status == 'verified' %}
                                                <span class="badge bg-success">تم التحقق</span>
                                            {% else %}
                                                <span class="badge bg-danger">مرفوض</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if reading.odometer_image %}
                                                <i class="fas fa-camera text-success" title="يوجد صورة"></i>
                                            {% else %}
                                                <i class="fas fa-camera text-muted" title="لا توجد صورة"></i>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'fleet:odometer_reading_detail' reading.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            {% if reading.status == 'pending' %}
                                                <a href="{% url 'fleet:odometer_reading_verify' reading.pk %}" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check"></i> تحقق
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-tachometer-alt fa-3x mb-3"></i>
                                                <h5>لا توجد قراءات مسجلة</h5>
                                                <p>ابدأ بتسجيل قراءة العداد الأولى</p>
                                                <a href="{% url 'fleet:odometer_reading_create' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> تسجيل قراءة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
