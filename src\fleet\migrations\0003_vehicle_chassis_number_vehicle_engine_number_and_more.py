# Generated by Django 5.2.3 on 2025-06-10 12:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fleet', '0002_remove_vehicle_driver_driver_alter_incident_driver_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='vehicle',
            name='chassis_number',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='رقم الشاسية'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='engine_number',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='رقم الموتور'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='fuel_type',
            field=models.CharField(choices=[('gasoline', 'بنزين'), ('diesel', 'ديزل'), ('hybrid', 'هجين'), ('electric', 'كهربائي'), ('lpg', 'غاز البترول المسال'), ('cng', 'غاز طبيعي مضغوط')], default='gasoline', max_length=20, verbose_name='نوع الوقود'),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='model',
            field=models.CharField(max_length=100, verbose_name='الموديل'),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='plate_number',
            field=models.CharField(max_length=20, unique=True, verbose_name='رقم اللوحة'),
        ),
        migrations.CreateModel(
            name='FuelConsumption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consumption_rate', models.DecimalField(decimal_places=2, max_digits=6, verbose_name='معدل الاستهلاك (لتر/100كم)')),
                ('distance_traveled', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المسافة المقطوعة (كم)')),
                ('fuel_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='كمية الوقود (لتر)')),
                ('measurement_date', models.DateTimeField(verbose_name='تاريخ المعايرة')),
                ('period_start', models.DateField(blank=True, null=True, verbose_name='بداية فترة القياس')),
                ('period_end', models.DateField(blank=True, null=True, verbose_name='نهاية فترة القياس')),
                ('measurement_type', models.CharField(choices=[('manual', 'قياس يدوي'), ('automatic', 'قياس تلقائي'), ('estimated', 'تقدير')], default='manual', max_length=20, verbose_name='نوع القياس')),
                ('odometer_reading', models.IntegerField(blank=True, null=True, verbose_name='قراءة العداد (كم)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='البيان/الملاحظات')),
                ('weather_condition', models.CharField(blank=True, max_length=50, null=True, verbose_name='حالة الطقس')),
                ('road_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع الطريق')),
                ('load_condition', models.CharField(blank=True, max_length=50, null=True, verbose_name='حالة التحميل')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recorded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fuel_measurements', to='fleet.userprofile', verbose_name='تم التسجيل بواسطة')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fuel_consumptions', to='fleet.vehicle', verbose_name='المركبة')),
            ],
            options={
                'verbose_name': 'قياس استهلاك الوقود',
                'verbose_name_plural': 'قياسات استهلاك الوقود',
                'ordering': ['-measurement_date'],
                'indexes': [models.Index(fields=['vehicle', '-measurement_date'], name='fleet_fuelc_vehicle_4033a7_idx'), models.Index(fields=['measurement_date'], name='fleet_fuelc_measure_ec9427_idx')],
            },
        ),
    ]
