from django.contrib import admin
from .models import (
    Company, UserProfile, Vehicle, Trip, 
    Maintenance, Incident, IncidentImage, LocationLog
)

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'activity')
    search_fields = ('name', 'activity')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'company', 'role', 'phone')
    list_filter = ('role', 'company')
    search_fields = ('user__username', 'user__email', 'phone')

@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('plate_number', 'model', 'company', 'vehicle_type', 'status', 'driver')
    list_filter = ('status', 'vehicle_type', 'company')
    search_fields = ('plate_number', 'model')

@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'start_time', 'status')
    list_filter = ('status', 'company')
    search_fields = ('vehicle__plate_number', 'driver__user__username')

@admin.register(Maintenance)
class MaintenanceAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'scheduled_date', 'status', 'cost')
    list_filter = ('status', 'scheduled_date')
    search_fields = ('vehicle__plate_number', 'description')

@admin.register(Incident)
class IncidentAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'date_time', 'status')
    list_filter = ('status',)
    search_fields = ('vehicle__plate_number', 'description')

admin.site.register(IncidentImage)
admin.site.register(LocationLog)
