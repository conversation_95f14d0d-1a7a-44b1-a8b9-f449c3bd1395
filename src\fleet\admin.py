from django.contrib import admin
from django.utils import timezone
from .models import (
    Company, UserProfile, Vehicle, Trip,
    Maintenance, Incident, IncidentImage, LocationLog,
    Driver, VehicleDriverAssignment, FuelConsumption, DailyOdometerReading, FuelRefill,
    DailyRoute, RouteWaypoint
)

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'activity')
    search_fields = ('name', 'activity')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'company', 'role', 'phone')
    list_filter = ('role', 'company')
    search_fields = ('user__username', 'user__email', 'phone')

@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'national_id', 'license_number', 'license_type', 'status', 'company', 'hire_date')
    list_filter = ('status', 'license_type', 'company', 'hire_date')
    search_fields = ('first_name', 'last_name', 'national_id', 'license_number', 'phone')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('معلومات شخصية', {
            'fields': ('first_name', 'last_name', 'national_id', 'phone', 'email', 'address', 'birth_date')
        }),
        ('معلومات الرخصة', {
            'fields': ('license_number', 'license_type', 'license_issue_date', 'license_expiry_date')
        }),
        ('معلومات العمل', {
            'fields': ('company', 'user_profile', 'hire_date', 'salary', 'status')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'user_profile')


class VehicleDriverAssignmentInline(admin.TabularInline):
    model = VehicleDriverAssignment
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('driver', 'start_date', 'end_date', 'status', 'notes', 'assigned_by')


class FuelConsumptionInline(admin.TabularInline):
    model = FuelConsumption
    extra = 0
    readonly_fields = ('efficiency_rating', 'created_at', 'updated_at')
    fields = ('measurement_date', 'consumption_rate', 'distance_traveled', 'fuel_amount', 'efficiency_rating', 'notes')


class DailyOdometerReadingInline(admin.TabularInline):
    model = DailyOdometerReading
    extra = 0
    readonly_fields = ('daily_distance', 'distance_status', 'created_at', 'updated_at')
    fields = ('reading_date', 'driver', 'odometer_reading', 'daily_distance', 'shift', 'status', 'distance_status')


class FuelRefillInline(admin.TabularInline):
    model = FuelRefill
    extra = 0
    readonly_fields = ('total_amount', 'fuel_efficiency_estimate', 'created_at', 'updated_at')
    fields = ('refill_date', 'driver', 'fuel_type', 'quantity', 'price_per_liter', 'total_amount', 'status', 'fuel_efficiency_estimate')


class DailyRouteInline(admin.TabularInline):
    model = DailyRoute
    extra = 0
    readonly_fields = ('actual_distance', 'efficiency_rating', 'created_at', 'updated_at')
    fields = ('route_date', 'route_name', 'driver', 'route_type', 'status', 'efficiency_rating', 'actual_distance')


@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('plate_number', 'model', 'company', 'vehicle_type', 'status', 'get_current_driver', 'get_latest_consumption')
    list_filter = ('status', 'vehicle_type', 'fuel_type', 'company')
    search_fields = ('plate_number', 'model', 'chassis_number', 'engine_number')
    inlines = [VehicleDriverAssignmentInline, FuelConsumptionInline, DailyOdometerReadingInline, FuelRefillInline, DailyRouteInline]

    def get_current_driver(self, obj):
        current_driver = obj.current_driver
        return current_driver.full_name if current_driver else 'لا يوجد سائق'
    get_current_driver.short_description = 'السائق الحالي'

    def get_latest_consumption(self, obj):
        latest = obj.latest_fuel_consumption
        if latest:
            return f"{latest.consumption_rate} لتر/100كم"
        return 'لا يوجد'
    get_latest_consumption.short_description = 'آخر استهلاك'


@admin.register(VehicleDriverAssignment)
class VehicleDriverAssignmentAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'driver', 'start_date', 'end_date', 'status', 'is_active')
    list_filter = ('status', 'start_date', 'end_date', 'vehicle__company')
    search_fields = ('vehicle__plate_number', 'driver__first_name', 'driver__last_name', 'driver__license_number')
    readonly_fields = ('created_at', 'updated_at', 'duration')

    fieldsets = (
        ('معلومات التعيين', {
            'fields': ('vehicle', 'driver', 'assigned_by')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date', 'duration')
        }),
        ('التفاصيل', {
            'fields': ('status', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'driver', 'assigned_by')

    actions = ['end_assignment']

    def end_assignment(self, request, queryset):
        """إنهاء التعيينات المحددة"""
        updated = 0
        for assignment in queryset.filter(end_date__isnull=True):
            assignment.end_date = timezone.now()
            assignment.status = 'completed'
            assignment.save()
            updated += 1

        self.message_user(request, f'تم إنهاء {updated} تعيين بنجاح.')
    end_assignment.short_description = 'إنهاء التعيينات المحددة'


@admin.register(FuelConsumption)
class FuelConsumptionAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'consumption_rate', 'measurement_date', 'efficiency_rating', 'measurement_type', 'recorded_by')
    list_filter = ('measurement_type', 'measurement_date', 'vehicle__company', 'vehicle__fuel_type')
    search_fields = ('vehicle__plate_number', 'vehicle__model', 'notes')
    readonly_fields = ('efficiency_rating', 'efficiency_color', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات القياس', {
            'fields': ('vehicle', 'measurement_date', 'measurement_type', 'recorded_by')
        }),
        ('بيانات الاستهلاك', {
            'fields': ('consumption_rate', 'distance_traveled', 'fuel_amount', 'odometer_reading')
        }),
        ('فترة القياس', {
            'fields': ('period_start', 'period_end'),
            'classes': ('collapse',)
        }),
        ('ظروف القياس', {
            'fields': ('weather_condition', 'road_type', 'load_condition'),
            'classes': ('collapse',)
        }),
        ('تقييم الكفاءة', {
            'fields': ('efficiency_rating', 'efficiency_color'),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'recorded_by')

    actions = ['mark_as_manual', 'mark_as_automatic']

    def mark_as_manual(self, request, queryset):
        updated = queryset.update(measurement_type='manual')
        self.message_user(request, f'تم تحديث {updated} قياس كقياس يدوي.')
    mark_as_manual.short_description = 'تحديد كقياس يدوي'

    def mark_as_automatic(self, request, queryset):
        updated = queryset.update(measurement_type='automatic')
        self.message_user(request, f'تم تحديث {updated} قياس كقياس تلقائي.')
    mark_as_automatic.short_description = 'تحديد كقياس تلقائي'


@admin.register(DailyOdometerReading)
class DailyOdometerReadingAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'driver', 'reading_date', 'odometer_reading', 'daily_distance', 'distance_status', 'status', 'shift')
    list_filter = ('status', 'shift', 'reading_date', 'vehicle__company')
    search_fields = ('vehicle__plate_number', 'driver__first_name', 'driver__last_name', 'notes')
    readonly_fields = ('daily_distance', 'distance_status', 'distance_color', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('vehicle', 'driver', 'reading_date', 'shift')
        }),
        ('بيانات القراءة', {
            'fields': ('odometer_reading', 'previous_reading', 'daily_distance', 'distance_status')
        }),
        ('أوقات الوردية', {
            'fields': ('start_time', 'end_time'),
            'classes': ('collapse',)
        }),
        ('صورة ومعلومات إضافية', {
            'fields': ('odometer_image', 'fuel_level', 'location', 'notes')
        }),
        ('حالة التسجيل', {
            'fields': ('status', 'verified_by', 'verification_date', 'rejection_reason')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'driver', 'verified_by')

    actions = ['verify_readings', 'reject_readings']

    def verify_readings(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='verified',
            verified_by=request.user.userprofile,
            verification_date=timezone.now()
        )
        self.message_user(request, f'تم التحقق من {updated} قراءة.')
    verify_readings.short_description = 'التحقق من القراءات المحددة'

    def reject_readings(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='rejected',
            verified_by=request.user.userprofile,
            verification_date=timezone.now(),
            rejection_reason='تم الرفض من لوحة الإدارة'
        )
        self.message_user(request, f'تم رفض {updated} قراءة.')
    reject_readings.short_description = 'رفض القراءات المحددة'


@admin.register(FuelRefill)
class FuelRefillAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'driver', 'refill_date', 'fuel_type', 'quantity', 'total_amount', 'payment_display', 'status')
    list_filter = ('status', 'fuel_type', 'payment_method', 'refill_date', 'vehicle__company')
    search_fields = ('vehicle__plate_number', 'driver__first_name', 'driver__last_name', 'station_name', 'transaction_id')
    readonly_fields = ('fuel_efficiency_estimate', 'cost_per_km', 'payment_display', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('vehicle', 'driver', 'refill_date', 'fuel_type')
        }),
        ('بيانات التزود', {
            'fields': ('quantity', 'price_per_liter', 'total_amount', 'odometer_reading')
        }),
        ('معلومات المحطة', {
            'fields': ('station_name', 'station_location', 'pump_number', 'pump_receipt_image'),
            'classes': ('collapse',)
        }),
        ('بيانات الدفع', {
            'fields': ('payment_method', 'card_last_four', 'card_type', 'transaction_id', 'approval_code', 'payment_display')
        }),
        ('معلومات إضافية', {
            'fields': ('receipt_number', 'notes'),
            'classes': ('collapse',)
        }),
        ('تقييم الكفاءة', {
            'fields': ('fuel_efficiency_estimate', 'cost_per_km'),
            'classes': ('collapse',)
        }),
        ('حالة التسجيل', {
            'fields': ('status', 'approved_by', 'approval_date', 'rejection_reason')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'driver', 'approved_by')

    actions = ['approve_refills', 'reject_refills']

    def approve_refills(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status__in=['pending', 'under_review']).update(
            status='approved',
            approved_by=request.user.userprofile,
            approval_date=timezone.now()
        )
        self.message_user(request, f'تمت الموافقة على {updated} عملية تزود.')
    approve_refills.short_description = 'الموافقة على عمليات التزود المحددة'

    def reject_refills(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status__in=['pending', 'under_review']).update(
            status='rejected',
            approved_by=request.user.userprofile,
            approval_date=timezone.now(),
            rejection_reason='تم الرفض من لوحة الإدارة'
        )
        self.message_user(request, f'تم رفض {updated} عملية تزود.')
    reject_refills.short_description = 'رفض عمليات التزود المحددة'


class RouteWaypointInline(admin.TabularInline):
    model = RouteWaypoint
    extra = 0
    readonly_fields = ('status_color', 'created_at', 'updated_at')
    fields = ('order', 'name', 'waypoint_type', 'address', 'planned_arrival_time', 'planned_departure_time', 'status', 'status_color')


@admin.register(DailyRoute)
class DailyRouteAdmin(admin.ModelAdmin):
    list_display = ('route_name', 'vehicle', 'driver', 'route_date', 'route_type', 'priority', 'status', 'efficiency_rating')
    list_filter = ('status', 'route_type', 'priority', 'route_date', 'vehicle__company')
    search_fields = ('route_name', 'vehicle__plate_number', 'driver__first_name', 'driver__last_name', 'client_name')
    readonly_fields = ('actual_distance', 'actual_duration', 'planned_duration', 'efficiency_rating', 'status_color', 'priority_color', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('vehicle', 'driver', 'route_date', 'route_name', 'route_type', 'priority')
        }),
        ('نقاط البداية والنهاية', {
            'fields': (
                ('start_location', 'end_location'),
                ('start_latitude', 'start_longitude'),
                ('end_latitude', 'end_longitude')
            )
        }),
        ('الأوقات المخططة', {
            'fields': ('planned_start_time', 'planned_end_time', 'planned_duration')
        }),
        ('الأوقات الفعلية', {
            'fields': ('actual_start_time', 'actual_end_time', 'actual_duration'),
            'classes': ('collapse',)
        }),
        ('قراءات العداد', {
            'fields': ('start_odometer', 'end_odometer', 'actual_distance'),
            'classes': ('collapse',)
        }),
        ('التقديرات والتكلفة', {
            'fields': ('estimated_distance', 'estimated_duration', 'estimated_cost', 'actual_cost')
        }),
        ('معلومات العميل', {
            'fields': ('client_name', 'client_phone', 'client_address'),
            'classes': ('collapse',)
        }),
        ('الوصف والملاحظات', {
            'fields': ('description', 'notes')
        }),
        ('حالة الرحلة', {
            'fields': ('status', 'status_color', 'priority_color', 'efficiency_rating')
        }),
        ('تواريخ النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [RouteWaypointInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'driver', 'created_by')

    actions = ['start_routes', 'complete_routes', 'cancel_routes']

    def start_routes(self, request, queryset):
        updated = queryset.filter(status='planned').update(status='in_progress')
        self.message_user(request, f'تم بدء {updated} رحلة.')
    start_routes.short_description = 'بدء الرحلات المحددة'

    def complete_routes(self, request, queryset):
        updated = queryset.filter(status='in_progress').update(status='completed')
        self.message_user(request, f'تم إكمال {updated} رحلة.')
    complete_routes.short_description = 'إكمال الرحلات المحددة'

    def cancel_routes(self, request, queryset):
        updated = queryset.filter(status__in=['planned', 'in_progress']).update(status='cancelled')
        self.message_user(request, f'تم إلغاء {updated} رحلة.')
    cancel_routes.short_description = 'إلغاء الرحلات المحددة'


@admin.register(RouteWaypoint)
class RouteWaypointAdmin(admin.ModelAdmin):
    list_display = ('route', 'name', 'waypoint_type', 'order', 'planned_arrival_time', 'actual_arrival_time', 'status')
    list_filter = ('waypoint_type', 'status', 'route__route_date')
    search_fields = ('name', 'address', 'route__route_name', 'contact_person')
    readonly_fields = ('status_color', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('route', 'name', 'waypoint_type', 'order')
        }),
        ('الموقع', {
            'fields': ('address', 'latitude', 'longitude')
        }),
        ('الأوقات', {
            'fields': ('planned_arrival_time', 'actual_arrival_time', 'planned_departure_time', 'actual_departure_time')
        }),
        ('معلومات الاتصال', {
            'fields': ('contact_person', 'contact_phone'),
            'classes': ('collapse',)
        }),
        ('تعليمات وملاحظات', {
            'fields': ('special_instructions', 'notes')
        }),
        ('الحالة', {
            'fields': ('status', 'status_color')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_reached', 'mark_completed', 'skip_waypoints']

    def mark_reached(self, request, queryset):
        updated = queryset.filter(status='pending').update(status='reached')
        self.message_user(request, f'تم تسجيل الوصول لـ {updated} نقطة.')
    mark_reached.short_description = 'تسجيل الوصول للنقاط المحددة'

    def mark_completed(self, request, queryset):
        updated = queryset.filter(status='reached').update(status='completed')
        self.message_user(request, f'تم إكمال {updated} نقطة.')
    mark_completed.short_description = 'إكمال النقاط المحددة'

    def skip_waypoints(self, request, queryset):
        updated = queryset.filter(status='pending').update(status='skipped')
        self.message_user(request, f'تم تجاوز {updated} نقطة.')
    skip_waypoints.short_description = 'تجاوز النقاط المحددة'

@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'start_time', 'status')
    list_filter = ('status', 'company')
    search_fields = ('vehicle__plate_number', 'driver__user__username')

@admin.register(Maintenance)
class MaintenanceAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'scheduled_date', 'status', 'cost')
    list_filter = ('status', 'scheduled_date')
    search_fields = ('vehicle__plate_number', 'description')

@admin.register(Incident)
class IncidentAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'date_time', 'status')
    list_filter = ('status',)
    search_fields = ('vehicle__plate_number', 'description')

admin.site.register(IncidentImage)
admin.site.register(LocationLog)
