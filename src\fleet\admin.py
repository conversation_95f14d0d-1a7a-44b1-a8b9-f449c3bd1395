from django.contrib import admin
from django.utils import timezone
from .models import (
    Company, UserProfile, Vehicle, Trip,
    Maintenance, Incident, IncidentImage, LocationLog,
    Driver, VehicleDriverAssignment
)

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'phone', 'activity')
    search_fields = ('name', 'activity')

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'company', 'role', 'phone')
    list_filter = ('role', 'company')
    search_fields = ('user__username', 'user__email', 'phone')

@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'national_id', 'license_number', 'license_type', 'status', 'company', 'hire_date')
    list_filter = ('status', 'license_type', 'company', 'hire_date')
    search_fields = ('first_name', 'last_name', 'national_id', 'license_number', 'phone')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('معلومات شخصية', {
            'fields': ('first_name', 'last_name', 'national_id', 'phone', 'email', 'address', 'birth_date')
        }),
        ('معلومات الرخصة', {
            'fields': ('license_number', 'license_type', 'license_issue_date', 'license_expiry_date')
        }),
        ('معلومات العمل', {
            'fields': ('company', 'user_profile', 'hire_date', 'salary', 'status')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'user_profile')


class VehicleDriverAssignmentInline(admin.TabularInline):
    model = VehicleDriverAssignment
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('driver', 'start_date', 'end_date', 'status', 'notes', 'assigned_by')


@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('plate_number', 'model', 'company', 'vehicle_type', 'status', 'get_current_driver')
    list_filter = ('status', 'vehicle_type', 'company')
    search_fields = ('plate_number', 'model')
    inlines = [VehicleDriverAssignmentInline]

    def get_current_driver(self, obj):
        current_driver = obj.current_driver
        return current_driver.full_name if current_driver else 'لا يوجد سائق'
    get_current_driver.short_description = 'السائق الحالي'


@admin.register(VehicleDriverAssignment)
class VehicleDriverAssignmentAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'driver', 'start_date', 'end_date', 'status', 'is_active')
    list_filter = ('status', 'start_date', 'end_date', 'vehicle__company')
    search_fields = ('vehicle__plate_number', 'driver__first_name', 'driver__last_name', 'driver__license_number')
    readonly_fields = ('created_at', 'updated_at', 'duration')

    fieldsets = (
        ('معلومات التعيين', {
            'fields': ('vehicle', 'driver', 'assigned_by')
        }),
        ('التواريخ', {
            'fields': ('start_date', 'end_date', 'duration')
        }),
        ('التفاصيل', {
            'fields': ('status', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('vehicle', 'driver', 'assigned_by')

    actions = ['end_assignment']

    def end_assignment(self, request, queryset):
        """إنهاء التعيينات المحددة"""
        updated = 0
        for assignment in queryset.filter(end_date__isnull=True):
            assignment.end_date = timezone.now()
            assignment.status = 'completed'
            assignment.save()
            updated += 1

        self.message_user(request, f'تم إنهاء {updated} تعيين بنجاح.')
    end_assignment.short_description = 'إنهاء التعيينات المحددة'

@admin.register(Trip)
class TripAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'start_time', 'status')
    list_filter = ('status', 'company')
    search_fields = ('vehicle__plate_number', 'driver__user__username')

@admin.register(Maintenance)
class MaintenanceAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'scheduled_date', 'status', 'cost')
    list_filter = ('status', 'scheduled_date')
    search_fields = ('vehicle__plate_number', 'description')

@admin.register(Incident)
class IncidentAdmin(admin.ModelAdmin):
    list_display = ('id', 'vehicle', 'driver', 'date_time', 'status')
    list_filter = ('status',)
    search_fields = ('vehicle__plate_number', 'description')

admin.site.register(IncidentImage)
admin.site.register(LocationLog)
