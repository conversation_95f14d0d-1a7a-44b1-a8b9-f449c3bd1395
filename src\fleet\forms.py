from django import forms
from django.utils import timezone
from .models import Driver, Vehicle, VehicleDriverAssignment


class DriverForm(forms.ModelForm):
    """نموذج إضافة وتعديل السائقين"""
    
    class Meta:
        model = Driver
        fields = [
            'first_name', 'last_name', 'national_id', 'phone', 'email', 'address', 'birth_date',
            'license_number', 'license_type', 'license_issue_date', 'license_expiry_date',
            'hire_date', 'salary', 'status'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية الوطنية'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '05xxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان الكامل'
            }),
            'birth_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم رخصة القيادة'
            }),
            'license_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'license_issue_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'salary': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
        }
        
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'national_id': 'رقم الهوية',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'birth_date': 'تاريخ الميلاد',
            'license_number': 'رقم الرخصة',
            'license_type': 'نوع الرخصة',
            'license_issue_date': 'تاريخ إصدار الرخصة',
            'license_expiry_date': 'تاريخ انتهاء الرخصة',
            'hire_date': 'تاريخ التوظيف',
            'salary': 'الراتب',
            'status': 'الحالة',
        }

    def clean_national_id(self):
        national_id = self.cleaned_data['national_id']
        if len(national_id) != 10:
            raise forms.ValidationError('رقم الهوية يجب أن يكون 10 أرقام')
        if not national_id.isdigit():
            raise forms.ValidationError('رقم الهوية يجب أن يحتوي على أرقام فقط')
        return national_id

    def clean_phone(self):
        phone = self.cleaned_data['phone']
        if not phone.startswith('05') or len(phone) != 10:
            raise forms.ValidationError('رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام')
        return phone

    def clean_license_expiry_date(self):
        expiry_date = self.cleaned_data['license_expiry_date']
        if expiry_date < timezone.now().date():
            raise forms.ValidationError('تاريخ انتهاء الرخصة لا يمكن أن يكون في الماضي')
        return expiry_date


class VehicleForm(forms.ModelForm):
    """نموذج إضافة وتعديل المركبات"""

    class Meta:
        model = Vehicle
        fields = [
            'vehicle_type', 'plate_number', 'model', 'chassis_number', 'engine_number',
            'fuel_type', 'status', 'capacity', 'last_maintenance', 'insurance_expiry'
        ]
        
        widgets = {
            'vehicle_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'plate_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ABC-123'
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'تويوتا كامري 2022'
            }),
            'chassis_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'JTDKN3DP0A0123456'
            }),
            'engine_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '2GR-FE-1234567'
            }),
            'fuel_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'capacity': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '5 أشخاص أو 2 طن'
            }),
            'last_maintenance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'insurance_expiry': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
        
        labels = {
            'vehicle_type': 'نوع المركبة',
            'plate_number': 'رقم اللوحة',
            'model': 'الموديل',
            'chassis_number': 'رقم الشاسية',
            'engine_number': 'رقم الموتور',
            'fuel_type': 'نوع الوقود',
            'status': 'الحالة',
            'capacity': 'السعة',
            'last_maintenance': 'آخر صيانة',
            'insurance_expiry': 'انتهاء التأمين',
        }

    def clean_plate_number(self):
        plate_number = self.cleaned_data['plate_number']
        # التحقق من عدم تكرار رقم اللوحة
        if Vehicle.objects.filter(plate_number=plate_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم اللوحة موجود مسبقاً')
        return plate_number

    def clean_chassis_number(self):
        chassis_number = self.cleaned_data['chassis_number']
        # التحقق من عدم تكرار رقم الشاسية
        if Vehicle.objects.filter(chassis_number=chassis_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الشاسية موجود مسبقاً')

        # التحقق من طول رقم الشاسية (عادة 17 حرف)
        if len(chassis_number) < 10:
            raise forms.ValidationError('رقم الشاسية يجب أن يكون على الأقل 10 أحرف')

        return chassis_number

    def clean_engine_number(self):
        engine_number = self.cleaned_data['engine_number']
        # التحقق من عدم تكرار رقم الموتور
        if Vehicle.objects.filter(engine_number=engine_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الموتور موجود مسبقاً')

        # التحقق من طول رقم الموتور
        if len(engine_number) < 5:
            raise forms.ValidationError('رقم الموتور يجب أن يكون على الأقل 5 أحرف')

        return engine_number


class VehicleDriverAssignmentForm(forms.ModelForm):
    """نموذج تعيين سائق لمركبة"""
    
    class Meta:
        model = VehicleDriverAssignment
        fields = ['vehicle', 'driver', 'start_date', 'notes']
        
        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول التعيين'
            }),
        }
        
        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'start_date': 'تاريخ بدء التعيين',
            'notes': 'البيان/الملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        start_date = cleaned_data.get('start_date')
        
        if vehicle and start_date:
            # التحقق من عدم وجود تعيين نشط للمركبة
            existing_assignment = VehicleDriverAssignment.objects.filter(
                vehicle=vehicle,
                end_date__isnull=True
            ).exclude(pk=self.instance.pk)
            
            if existing_assignment.exists():
                raise forms.ValidationError('هذه المركبة لديها تعيين نشط بالفعل')
        
        return cleaned_data
