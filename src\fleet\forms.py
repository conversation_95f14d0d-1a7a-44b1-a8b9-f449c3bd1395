from django import forms
from django.utils import timezone
from .models import Driver, Vehicle, VehicleDriverAssignment, FuelConsumption, DailyOdometerReading


class DriverForm(forms.ModelForm):
    """نموذج إضافة وتعديل السائقين"""
    
    class Meta:
        model = Driver
        fields = [
            'first_name', 'last_name', 'national_id', 'phone', 'email', 'address', 'birth_date',
            'license_number', 'license_type', 'license_issue_date', 'license_expiry_date',
            'hire_date', 'salary', 'status'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية الوطنية'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '05xxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان الكامل'
            }),
            'birth_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم رخصة القيادة'
            }),
            'license_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'license_issue_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'salary': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
        }
        
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'national_id': 'رقم الهوية',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'birth_date': 'تاريخ الميلاد',
            'license_number': 'رقم الرخصة',
            'license_type': 'نوع الرخصة',
            'license_issue_date': 'تاريخ إصدار الرخصة',
            'license_expiry_date': 'تاريخ انتهاء الرخصة',
            'hire_date': 'تاريخ التوظيف',
            'salary': 'الراتب',
            'status': 'الحالة',
        }

    def clean_national_id(self):
        national_id = self.cleaned_data['national_id']
        if len(national_id) != 10:
            raise forms.ValidationError('رقم الهوية يجب أن يكون 10 أرقام')
        if not national_id.isdigit():
            raise forms.ValidationError('رقم الهوية يجب أن يحتوي على أرقام فقط')
        return national_id

    def clean_phone(self):
        phone = self.cleaned_data['phone']
        if not phone.startswith('05') or len(phone) != 10:
            raise forms.ValidationError('رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام')
        return phone

    def clean_license_expiry_date(self):
        expiry_date = self.cleaned_data['license_expiry_date']
        if expiry_date < timezone.now().date():
            raise forms.ValidationError('تاريخ انتهاء الرخصة لا يمكن أن يكون في الماضي')
        return expiry_date


class VehicleForm(forms.ModelForm):
    """نموذج إضافة وتعديل المركبات"""

    class Meta:
        model = Vehicle
        fields = [
            'vehicle_type', 'plate_number', 'model', 'chassis_number', 'engine_number',
            'fuel_type', 'status', 'capacity', 'last_maintenance', 'insurance_expiry'
        ]
        
        widgets = {
            'vehicle_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'plate_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ABC-123'
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'تويوتا كامري 2022'
            }),
            'chassis_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'JTDKN3DP0A0123456'
            }),
            'engine_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '2GR-FE-1234567'
            }),
            'fuel_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'capacity': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '5 أشخاص أو 2 طن'
            }),
            'last_maintenance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'insurance_expiry': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
        
        labels = {
            'vehicle_type': 'نوع المركبة',
            'plate_number': 'رقم اللوحة',
            'model': 'الموديل',
            'chassis_number': 'رقم الشاسية',
            'engine_number': 'رقم الموتور',
            'fuel_type': 'نوع الوقود',
            'status': 'الحالة',
            'capacity': 'السعة',
            'last_maintenance': 'آخر صيانة',
            'insurance_expiry': 'انتهاء التأمين',
        }

    def clean_plate_number(self):
        plate_number = self.cleaned_data['plate_number']
        # التحقق من عدم تكرار رقم اللوحة
        if Vehicle.objects.filter(plate_number=plate_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم اللوحة موجود مسبقاً')
        return plate_number

    def clean_chassis_number(self):
        chassis_number = self.cleaned_data['chassis_number']
        # التحقق من عدم تكرار رقم الشاسية
        if Vehicle.objects.filter(chassis_number=chassis_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الشاسية موجود مسبقاً')

        # التحقق من طول رقم الشاسية (عادة 17 حرف)
        if len(chassis_number) < 10:
            raise forms.ValidationError('رقم الشاسية يجب أن يكون على الأقل 10 أحرف')

        return chassis_number

    def clean_engine_number(self):
        engine_number = self.cleaned_data['engine_number']
        # التحقق من عدم تكرار رقم الموتور
        if Vehicle.objects.filter(engine_number=engine_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الموتور موجود مسبقاً')

        # التحقق من طول رقم الموتور
        if len(engine_number) < 5:
            raise forms.ValidationError('رقم الموتور يجب أن يكون على الأقل 5 أحرف')

        return engine_number


class VehicleDriverAssignmentForm(forms.ModelForm):
    """نموذج تعيين سائق لمركبة"""
    
    class Meta:
        model = VehicleDriverAssignment
        fields = ['vehicle', 'driver', 'start_date', 'notes']
        
        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول التعيين'
            }),
        }
        
        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'start_date': 'تاريخ بدء التعيين',
            'notes': 'البيان/الملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        start_date = cleaned_data.get('start_date')
        
        if vehicle and start_date:
            # التحقق من عدم وجود تعيين نشط للمركبة
            existing_assignment = VehicleDriverAssignment.objects.filter(
                vehicle=vehicle,
                end_date__isnull=True
            ).exclude(pk=self.instance.pk)
            
            if existing_assignment.exists():
                raise forms.ValidationError('هذه المركبة لديها تعيين نشط بالفعل')
        
        return cleaned_data


class FuelConsumptionForm(forms.ModelForm):
    """نموذج تسجيل استهلاك الوقود"""

    class Meta:
        model = FuelConsumption
        fields = [
            'vehicle', 'consumption_rate', 'distance_traveled', 'fuel_amount',
            'measurement_date', 'period_start', 'period_end', 'measurement_type',
            'odometer_reading', 'weather_condition', 'road_type', 'load_condition', 'notes'
        ]

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'consumption_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '8.5',
                'step': '0.01',
                'min': '0'
            }),
            'distance_traveled': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '100.0',
                'step': '0.01',
                'min': '0'
            }),
            'fuel_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '50.0',
                'step': '0.01',
                'min': '0'
            }),
            'measurement_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'period_start': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'period_end': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'measurement_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'odometer_reading': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '50000',
                'min': '0'
            }),
            'weather_condition': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مشمس، ممطر، غائم'
            }),
            'road_type': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'طريق سريع، مدينة، مختلط'
            }),
            'load_condition': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'فارغ، نصف محمل، محمل بالكامل'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول القياس أو الظروف'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'consumption_rate': 'معدل الاستهلاك (لتر/100كم)',
            'distance_traveled': 'المسافة المقطوعة (كم)',
            'fuel_amount': 'كمية الوقود (لتر)',
            'measurement_date': 'تاريخ المعايرة',
            'period_start': 'بداية فترة القياس',
            'period_end': 'نهاية فترة القياس',
            'measurement_type': 'نوع القياس',
            'odometer_reading': 'قراءة العداد (كم)',
            'weather_condition': 'حالة الطقس',
            'road_type': 'نوع الطريق',
            'load_condition': 'حالة التحميل',
            'notes': 'البيان/الملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)

    def clean(self):
        cleaned_data = super().clean()
        consumption_rate = cleaned_data.get('consumption_rate')
        distance_traveled = cleaned_data.get('distance_traveled')
        fuel_amount = cleaned_data.get('fuel_amount')
        period_start = cleaned_data.get('period_start')
        period_end = cleaned_data.get('period_end')

        # التحقق من صحة فترة القياس
        if period_start and period_end and period_start > period_end:
            raise forms.ValidationError('تاريخ بداية الفترة يجب أن يكون قبل تاريخ النهاية')

        # التحقق من وجود بيانات كافية لحساب الاستهلاك
        if not consumption_rate:
            if not (distance_traveled and fuel_amount):
                raise forms.ValidationError('يجب إدخال معدل الاستهلاك أو (المسافة المقطوعة وكمية الوقود)')

        # التحقق من منطقية معدل الاستهلاك
        if consumption_rate and (consumption_rate < 1 or consumption_rate > 50):
            raise forms.ValidationError('معدل الاستهلاك يجب أن يكون بين 1 و 50 لتر/100كم')

        return cleaned_data


class DailyOdometerReadingForm(forms.ModelForm):
    """نموذج تسجيل قراءة عداد الكيلومتر اليومية"""

    class Meta:
        model = DailyOdometerReading
        fields = [
            'vehicle', 'driver', 'reading_date', 'odometer_reading', 'shift',
            'start_time', 'end_time', 'odometer_image', 'fuel_level', 'location', 'notes'
        ]

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'reading_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'odometer_reading': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '123456',
                'min': '0'
            }),
            'shift': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'end_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'odometer_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'fuel_level': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ممتلئ، نصف، ربع، فارغ'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الموقع الحالي'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'reading_date': 'تاريخ القراءة',
            'odometer_reading': 'قراءة العداد (كم)',
            'shift': 'الوردية',
            'start_time': 'وقت البداية',
            'end_time': 'وقت النهاية',
            'odometer_image': 'صورة العداد',
            'fuel_level': 'مستوى الوقود',
            'location': 'الموقع',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user_profile = kwargs.pop('user_profile', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

        # إذا كان المستخدم سائق، اختر سائقه تلقائياً
        if user_profile and hasattr(user_profile, 'driver_profile'):
            self.fields['driver'].initial = user_profile.driver_profile
            self.fields['driver'].widget.attrs['readonly'] = True

    def clean_odometer_reading(self):
        odometer_reading = self.cleaned_data['odometer_reading']
        vehicle = self.cleaned_data.get('vehicle')
        reading_date = self.cleaned_data.get('reading_date')

        if vehicle and reading_date:
            # الحصول على آخر قراءة للمركبة
            last_reading = DailyOdometerReading.objects.filter(
                vehicle=vehicle,
                reading_date__lt=reading_date,
                status='verified'
            ).order_by('-reading_date').first()

            if last_reading and odometer_reading < last_reading.odometer_reading:
                raise forms.ValidationError(
                    f'قراءة العداد لا يمكن أن تكون أقل من آخر قراءة ({last_reading.odometer_reading} كم)'
                )

        return odometer_reading

    def clean_reading_date(self):
        reading_date = self.cleaned_data['reading_date']

        # التحقق من عدم تسجيل قراءة لتاريخ مستقبلي
        from django.utils import timezone
        if reading_date > timezone.now().date():
            raise forms.ValidationError('لا يمكن تسجيل قراءة لتاريخ مستقبلي')

        return reading_date

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        driver = cleaned_data.get('driver')
        reading_date = cleaned_data.get('reading_date')
        shift = cleaned_data.get('shift')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        # التحقق من عدم تكرار القراءة لنفس المركبة والسائق في نفس اليوم والوردية
        if vehicle and driver and reading_date and shift:
            existing_reading = DailyOdometerReading.objects.filter(
                vehicle=vehicle,
                driver=driver,
                reading_date=reading_date,
                shift=shift
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing_reading.exists():
                raise forms.ValidationError(
                    'يوجد قراءة مسجلة بالفعل لهذه المركبة والسائق في نفس اليوم والوردية'
                )

        # التحقق من منطقية أوقات الوردية
        if start_time and end_time and start_time >= end_time:
            raise forms.ValidationError('وقت البداية يجب أن يكون قبل وقت النهاية')

        return cleaned_data
