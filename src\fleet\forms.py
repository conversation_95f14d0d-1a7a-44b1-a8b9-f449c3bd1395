from django import forms
from django.utils import timezone
from .models import Driver, Vehicle, VehicleDriverAssignment, FuelConsumption, DailyOdometerReading, FuelRefill, DailyRoute, RouteWaypoint


class DriverForm(forms.ModelForm):
    """نموذج إضافة وتعديل السائقين"""
    
    class Meta:
        model = Driver
        fields = [
            'first_name', 'last_name', 'national_id', 'phone', 'email', 'address', 'birth_date',
            'license_number', 'license_type', 'license_issue_date', 'license_expiry_date',
            'hire_date', 'salary', 'status'
        ]
        
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'national_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهوية الوطنية'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '05xxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان الكامل'
            }),
            'birth_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم رخصة القيادة'
            }),
            'license_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'license_issue_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'license_expiry_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'salary': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
        }
        
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'national_id': 'رقم الهوية',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'birth_date': 'تاريخ الميلاد',
            'license_number': 'رقم الرخصة',
            'license_type': 'نوع الرخصة',
            'license_issue_date': 'تاريخ إصدار الرخصة',
            'license_expiry_date': 'تاريخ انتهاء الرخصة',
            'hire_date': 'تاريخ التوظيف',
            'salary': 'الراتب',
            'status': 'الحالة',
        }

    def clean_national_id(self):
        national_id = self.cleaned_data['national_id']
        if len(national_id) != 10:
            raise forms.ValidationError('رقم الهوية يجب أن يكون 10 أرقام')
        if not national_id.isdigit():
            raise forms.ValidationError('رقم الهوية يجب أن يحتوي على أرقام فقط')
        return national_id

    def clean_phone(self):
        phone = self.cleaned_data['phone']
        if not phone.startswith('05') or len(phone) != 10:
            raise forms.ValidationError('رقم الهاتف يجب أن يبدأ بـ 05 ويكون 10 أرقام')
        return phone

    def clean_license_expiry_date(self):
        expiry_date = self.cleaned_data['license_expiry_date']
        if expiry_date < timezone.now().date():
            raise forms.ValidationError('تاريخ انتهاء الرخصة لا يمكن أن يكون في الماضي')
        return expiry_date


class VehicleForm(forms.ModelForm):
    """نموذج إضافة وتعديل المركبات"""

    class Meta:
        model = Vehicle
        fields = [
            'vehicle_type', 'plate_number', 'model', 'chassis_number', 'engine_number',
            'fuel_type', 'status', 'capacity', 'last_maintenance', 'insurance_expiry'
        ]
        
        widgets = {
            'vehicle_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'plate_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ABC-123'
            }),
            'model': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'تويوتا كامري 2022'
            }),
            'chassis_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'JTDKN3DP0A0123456'
            }),
            'engine_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '2GR-FE-1234567'
            }),
            'fuel_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'capacity': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '5 أشخاص أو 2 طن'
            }),
            'last_maintenance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'insurance_expiry': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
        
        labels = {
            'vehicle_type': 'نوع المركبة',
            'plate_number': 'رقم اللوحة',
            'model': 'الموديل',
            'chassis_number': 'رقم الشاسية',
            'engine_number': 'رقم الموتور',
            'fuel_type': 'نوع الوقود',
            'status': 'الحالة',
            'capacity': 'السعة',
            'last_maintenance': 'آخر صيانة',
            'insurance_expiry': 'انتهاء التأمين',
        }

    def clean_plate_number(self):
        plate_number = self.cleaned_data['plate_number']
        # التحقق من عدم تكرار رقم اللوحة
        if Vehicle.objects.filter(plate_number=plate_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم اللوحة موجود مسبقاً')
        return plate_number

    def clean_chassis_number(self):
        chassis_number = self.cleaned_data['chassis_number']
        # التحقق من عدم تكرار رقم الشاسية
        if Vehicle.objects.filter(chassis_number=chassis_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الشاسية موجود مسبقاً')

        # التحقق من طول رقم الشاسية (عادة 17 حرف)
        if len(chassis_number) < 10:
            raise forms.ValidationError('رقم الشاسية يجب أن يكون على الأقل 10 أحرف')

        return chassis_number

    def clean_engine_number(self):
        engine_number = self.cleaned_data['engine_number']
        # التحقق من عدم تكرار رقم الموتور
        if Vehicle.objects.filter(engine_number=engine_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError('رقم الموتور موجود مسبقاً')

        # التحقق من طول رقم الموتور
        if len(engine_number) < 5:
            raise forms.ValidationError('رقم الموتور يجب أن يكون على الأقل 5 أحرف')

        return engine_number


class VehicleDriverAssignmentForm(forms.ModelForm):
    """نموذج تعيين سائق لمركبة"""
    
    class Meta:
        model = VehicleDriverAssignment
        fields = ['vehicle', 'driver', 'start_date', 'notes']
        
        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول التعيين'
            }),
        }
        
        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'start_date': 'تاريخ بدء التعيين',
            'notes': 'البيان/الملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        start_date = cleaned_data.get('start_date')
        
        if vehicle and start_date:
            # التحقق من عدم وجود تعيين نشط للمركبة
            existing_assignment = VehicleDriverAssignment.objects.filter(
                vehicle=vehicle,
                end_date__isnull=True
            ).exclude(pk=self.instance.pk)
            
            if existing_assignment.exists():
                raise forms.ValidationError('هذه المركبة لديها تعيين نشط بالفعل')
        
        return cleaned_data


class FuelConsumptionForm(forms.ModelForm):
    """نموذج تسجيل استهلاك الوقود"""

    class Meta:
        model = FuelConsumption
        fields = [
            'vehicle', 'consumption_rate', 'distance_traveled', 'fuel_amount',
            'measurement_date', 'period_start', 'period_end', 'measurement_type',
            'odometer_reading', 'weather_condition', 'road_type', 'load_condition', 'notes'
        ]

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'consumption_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '8.5',
                'step': '0.01',
                'min': '0'
            }),
            'distance_traveled': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '100.0',
                'step': '0.01',
                'min': '0'
            }),
            'fuel_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '50.0',
                'step': '0.01',
                'min': '0'
            }),
            'measurement_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'period_start': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'period_end': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'measurement_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'odometer_reading': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '50000',
                'min': '0'
            }),
            'weather_condition': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مشمس، ممطر، غائم'
            }),
            'road_type': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'طريق سريع، مدينة، مختلط'
            }),
            'load_condition': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'فارغ، نصف محمل، محمل بالكامل'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات حول القياس أو الظروف'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'consumption_rate': 'معدل الاستهلاك (لتر/100كم)',
            'distance_traveled': 'المسافة المقطوعة (كم)',
            'fuel_amount': 'كمية الوقود (لتر)',
            'measurement_date': 'تاريخ المعايرة',
            'period_start': 'بداية فترة القياس',
            'period_end': 'نهاية فترة القياس',
            'measurement_type': 'نوع القياس',
            'odometer_reading': 'قراءة العداد (كم)',
            'weather_condition': 'حالة الطقس',
            'road_type': 'نوع الطريق',
            'load_condition': 'حالة التحميل',
            'notes': 'البيان/الملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)

    def clean(self):
        cleaned_data = super().clean()
        consumption_rate = cleaned_data.get('consumption_rate')
        distance_traveled = cleaned_data.get('distance_traveled')
        fuel_amount = cleaned_data.get('fuel_amount')
        period_start = cleaned_data.get('period_start')
        period_end = cleaned_data.get('period_end')

        # التحقق من صحة فترة القياس
        if period_start and period_end and period_start > period_end:
            raise forms.ValidationError('تاريخ بداية الفترة يجب أن يكون قبل تاريخ النهاية')

        # التحقق من وجود بيانات كافية لحساب الاستهلاك
        if not consumption_rate:
            if not (distance_traveled and fuel_amount):
                raise forms.ValidationError('يجب إدخال معدل الاستهلاك أو (المسافة المقطوعة وكمية الوقود)')

        # التحقق من منطقية معدل الاستهلاك
        if consumption_rate and (consumption_rate < 1 or consumption_rate > 50):
            raise forms.ValidationError('معدل الاستهلاك يجب أن يكون بين 1 و 50 لتر/100كم')

        return cleaned_data


class DailyOdometerReadingForm(forms.ModelForm):
    """نموذج تسجيل قراءة عداد الكيلومتر اليومية"""

    class Meta:
        model = DailyOdometerReading
        fields = [
            'vehicle', 'driver', 'reading_date', 'odometer_reading', 'shift',
            'start_time', 'end_time', 'odometer_image', 'fuel_level', 'location', 'notes'
        ]

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'reading_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'odometer_reading': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '123456',
                'min': '0'
            }),
            'shift': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'end_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'odometer_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'fuel_level': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'ممتلئ، نصف، ربع، فارغ'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الموقع الحالي'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'reading_date': 'تاريخ القراءة',
            'odometer_reading': 'قراءة العداد (كم)',
            'shift': 'الوردية',
            'start_time': 'وقت البداية',
            'end_time': 'وقت النهاية',
            'odometer_image': 'صورة العداد',
            'fuel_level': 'مستوى الوقود',
            'location': 'الموقع',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user_profile = kwargs.pop('user_profile', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

        # إذا كان المستخدم سائق، اختر سائقه تلقائياً
        if user_profile and hasattr(user_profile, 'driver_profile'):
            self.fields['driver'].initial = user_profile.driver_profile
            self.fields['driver'].widget.attrs['readonly'] = True

    def clean_odometer_reading(self):
        odometer_reading = self.cleaned_data['odometer_reading']
        vehicle = self.cleaned_data.get('vehicle')
        reading_date = self.cleaned_data.get('reading_date')

        if vehicle and reading_date:
            # الحصول على آخر قراءة للمركبة
            last_reading = DailyOdometerReading.objects.filter(
                vehicle=vehicle,
                reading_date__lt=reading_date,
                status='verified'
            ).order_by('-reading_date').first()

            if last_reading and odometer_reading < last_reading.odometer_reading:
                raise forms.ValidationError(
                    f'قراءة العداد لا يمكن أن تكون أقل من آخر قراءة ({last_reading.odometer_reading} كم)'
                )

        return odometer_reading

    def clean_reading_date(self):
        reading_date = self.cleaned_data['reading_date']

        # التحقق من عدم تسجيل قراءة لتاريخ مستقبلي
        from django.utils import timezone
        if reading_date > timezone.now().date():
            raise forms.ValidationError('لا يمكن تسجيل قراءة لتاريخ مستقبلي')

        return reading_date

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        driver = cleaned_data.get('driver')
        reading_date = cleaned_data.get('reading_date')
        shift = cleaned_data.get('shift')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        # التحقق من عدم تكرار القراءة لنفس المركبة والسائق في نفس اليوم والوردية
        if vehicle and driver and reading_date and shift:
            existing_reading = DailyOdometerReading.objects.filter(
                vehicle=vehicle,
                driver=driver,
                reading_date=reading_date,
                shift=shift
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing_reading.exists():
                raise forms.ValidationError(
                    'يوجد قراءة مسجلة بالفعل لهذه المركبة والسائق في نفس اليوم والوردية'
                )

        # التحقق من منطقية أوقات الوردية
        if start_time and end_time and start_time >= end_time:
            raise forms.ValidationError('وقت البداية يجب أن يكون قبل وقت النهاية')

        return cleaned_data


class FuelRefillForm(forms.ModelForm):
    """نموذج تسجيل التزود بالوقود"""

    class Meta:
        model = FuelRefill
        fields = [
            'vehicle', 'driver', 'refill_date', 'fuel_type', 'quantity', 'price_per_liter',
            'total_amount', 'odometer_reading', 'station_name', 'station_location',
            'pump_number', 'pump_receipt_image', 'payment_method', 'card_last_four',
            'card_type', 'transaction_id', 'approval_code', 'receipt_number', 'notes'
        ]

        # تحديد تنسيقات الإدخال
        input_formats = {
            'refill_date': ['%Y-%m-%dT%H:%M', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M'],
        }

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'refill_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }, format='%Y-%m-%dT%H:%M'),
            'fuel_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '50.00',
                'step': '0.01',
                'min': '0'
            }),
            'price_per_liter': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '2.340',
                'step': '0.001',
                'min': '0'
            }),
            'total_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '117.00',
                'step': '0.01',
                'min': '0',
                'readonly': True
            }),
            'odometer_reading': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '123456',
                'min': '0'
            }),
            'station_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أرامكو، شل، موبيل'
            }),
            'station_location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الرياض - طريق الملك فهد'
            }),
            'pump_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '5'
            }),
            'pump_receipt_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'payment_method': forms.Select(attrs={
                'class': 'form-select'
            }),
            'card_last_four': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '1234',
                'maxlength': '4'
            }),
            'card_type': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'فيزا، ماستركارد'
            }),
            'transaction_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'TXN123456789'
            }),
            'approval_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'APP123'
            }),
            'receipt_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'REC123456'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'refill_date': 'تاريخ ووقت التزود',
            'fuel_type': 'نوع الوقود',
            'quantity': 'الكمية (لتر)',
            'price_per_liter': 'سعر اللتر (ريال)',
            'total_amount': 'المبلغ الإجمالي (ريال)',
            'odometer_reading': 'قراءة العداد (كم)',
            'station_name': 'اسم المحطة',
            'station_location': 'موقع المحطة',
            'pump_number': 'رقم المضخة',
            'pump_receipt_image': 'صورة إيصال المضخة',
            'payment_method': 'طريقة الدفع',
            'card_last_four': 'آخر 4 أرقام من البطاقة',
            'card_type': 'نوع البطاقة',
            'transaction_id': 'رقم المعاملة',
            'approval_code': 'رمز الموافقة',
            'receipt_number': 'رقم الإيصال',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user_profile = kwargs.pop('user_profile', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

        # إذا كان المستخدم سائق، اختر سائقه تلقائياً
        if user_profile and hasattr(user_profile, 'driver_profile'):
            self.fields['driver'].initial = user_profile.driver_profile
            self.fields['driver'].widget.attrs['readonly'] = True

        # تعيين القيمة الافتراضية للتاريخ
        if not self.instance.pk:  # فقط للسجلات الجديدة
            from django.utils import timezone
            self.fields['refill_date'].initial = timezone.now()

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None:
            if quantity <= 0:
                raise forms.ValidationError('كمية الوقود يجب أن تكون أكبر من صفر')
            if quantity > 200:
                raise forms.ValidationError('كمية الوقود تبدو غير منطقية (أكثر من 200 لتر)')
        return quantity

    def clean_price_per_liter(self):
        price = self.cleaned_data.get('price_per_liter')
        if price is not None:
            if price <= 0:
                raise forms.ValidationError('سعر اللتر يجب أن يكون أكبر من صفر')
            if price > 10:
                raise forms.ValidationError('سعر اللتر يبدو غير منطقي (أكثر من 10 ريال)')
        return price

    def clean_card_last_four(self):
        card_last_four = self.cleaned_data.get('card_last_four')
        payment_method = self.cleaned_data.get('payment_method')

        if payment_method in ['visa', 'mastercard', 'company_card', 'fuel_card']:
            if card_last_four:
                if len(card_last_four) != 4:
                    raise forms.ValidationError('يجب إدخال 4 أرقام بالضبط')
                if not card_last_four.isdigit():
                    raise forms.ValidationError('يجب أن تحتوي على أرقام فقط')

        return card_last_four

    def clean_odometer_reading(self):
        odometer_reading = self.cleaned_data.get('odometer_reading')
        vehicle = self.cleaned_data.get('vehicle')

        if odometer_reading is not None and vehicle:
            # التحقق من آخر قراءة عداد للمركبة
            latest_reading = vehicle.latest_odometer_reading
            if latest_reading and odometer_reading < latest_reading.odometer_reading:
                raise forms.ValidationError(
                    f'قراءة العداد لا يمكن أن تكون أقل من آخر قراءة ({latest_reading.odometer_reading} كم)'
                )

        return odometer_reading

    def clean_refill_date(self):
        refill_date = self.cleaned_data.get('refill_date')

        if refill_date:
            from django.utils import timezone
            from datetime import timedelta

            # تحويل التاريخ إلى timezone-aware إذا لم يكن كذلك
            if timezone.is_naive(refill_date):
                refill_date = timezone.make_aware(refill_date)

            current_time = timezone.now()

            # التحقق من الحدود الزمنية
            max_future_time = current_time + timedelta(hours=24)
            min_past_time = current_time - timedelta(days=30)

            if refill_date > max_future_time:
                raise forms.ValidationError(
                    f'لا يمكن تسجيل تزود لأكثر من 24 ساعة في المستقبل'
                )

            if refill_date < min_past_time:
                raise forms.ValidationError(
                    f'لا يمكن تسجيل تزود لأكثر من 30 يوم في الماضي'
                )

        return refill_date

    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        price_per_liter = cleaned_data.get('price_per_liter')
        refill_date = cleaned_data.get('refill_date')

        # حساب المبلغ الإجمالي تلقائياً
        if quantity is not None and price_per_liter is not None:
            calculated_total = quantity * price_per_liter
            cleaned_data['total_amount'] = calculated_total

        # التاريخ تم التحقق منه في clean_refill_date

        # التحقق من منطقية التاريخ مع آخر عملية تزود
        vehicle = cleaned_data.get('vehicle')
        if vehicle and refill_date:
            # الحصول على آخر عملية تزود للمركبة
            last_refill = vehicle.fuel_refills.filter(
                status__in=['approved', 'pending']
            ).exclude(pk=self.instance.pk if self.instance else None).order_by('-refill_date').first()

            if last_refill:
                # التحقق من عدم تسجيل عمليتين في نفس الوقت تقريباً
                time_diff = abs((refill_date - last_refill.refill_date).total_seconds())
                if time_diff < 3600:  # أقل من ساعة
                    raise forms.ValidationError(
                        f'يوجد عملية تزود أخرى مسجلة في {last_refill.refill_date.strftime("%Y-%m-%d %H:%M")}. '
                        'يجب أن يكون الفرق ساعة على الأقل بين عمليات التزود.'
                    )

        return cleaned_data


class DailyRouteForm(forms.ModelForm):
    """نموذج تسجيل خط السير اليومي"""

    class Meta:
        model = DailyRoute
        fields = [
            'vehicle', 'driver', 'route_date', 'route_name', 'route_type', 'priority',
            'start_location', 'start_latitude', 'start_longitude',
            'end_location', 'end_latitude', 'end_longitude',
            'planned_start_time', 'planned_end_time',
            'estimated_distance', 'estimated_duration', 'estimated_cost',
            'client_name', 'client_phone', 'client_address',
            'description', 'notes'
        ]

        widgets = {
            'vehicle': forms.Select(attrs={
                'class': 'form-select'
            }),
            'driver': forms.Select(attrs={
                'class': 'form-select'
            }),
            'route_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'route_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'توصيل إلى الرياض'
            }),
            'route_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'start_location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الرياض - حي النخيل'
            }),
            'start_latitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '24.7136',
                'step': '0.00000001'
            }),
            'start_longitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '46.6753',
                'step': '0.00000001'
            }),
            'end_location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'جدة - حي الصفا'
            }),
            'end_latitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '21.3891',
                'step': '0.00000001'
            }),
            'end_longitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '39.8579',
                'step': '0.00000001'
            }),
            'planned_start_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'planned_end_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'estimated_distance': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '950',
                'step': '0.01',
                'min': '0'
            }),
            'estimated_duration': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '10:30:00 (ساعات:دقائق:ثواني)'
            }),
            'estimated_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '1500.00',
                'step': '0.01',
                'min': '0'
            }),
            'client_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'شركة النقل المتقدم'
            }),
            'client_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '0501234567'
            }),
            'client_address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'العنوان التفصيلي للعميل'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف تفصيلي للرحلة'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات إضافية'
            }),
        }

        labels = {
            'vehicle': 'المركبة',
            'driver': 'السائق',
            'route_date': 'تاريخ الرحلة',
            'route_name': 'اسم الرحلة',
            'route_type': 'نوع الرحلة',
            'priority': 'الأولوية',
            'start_location': 'نقطة البداية',
            'start_latitude': 'خط العرض - البداية',
            'start_longitude': 'خط الطول - البداية',
            'end_location': 'نقطة النهاية',
            'end_latitude': 'خط العرض - النهاية',
            'end_longitude': 'خط الطول - النهاية',
            'planned_start_time': 'الوقت المخطط للبداية',
            'planned_end_time': 'الوقت المخطط للنهاية',
            'estimated_distance': 'المسافة المقدرة (كم)',
            'estimated_duration': 'المدة المقدرة',
            'estimated_cost': 'التكلفة المقدرة (ريال)',
            'client_name': 'اسم العميل/الجهة',
            'client_phone': 'هاتف العميل',
            'client_address': 'عنوان العميل',
            'description': 'وصف الرحلة',
            'notes': 'ملاحظات',
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        user_profile = kwargs.pop('user_profile', None)
        super().__init__(*args, **kwargs)

        if company:
            # تصفية المركبات والسائقين حسب الشركة
            self.fields['vehicle'].queryset = Vehicle.objects.filter(company=company)
            self.fields['driver'].queryset = Driver.objects.filter(company=company, status='active')

        # إذا كان المستخدم سائق، اختر سائقه تلقائياً
        if user_profile and hasattr(user_profile, 'driver_profile'):
            self.fields['driver'].initial = user_profile.driver_profile
            self.fields['driver'].widget.attrs['readonly'] = True

        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.pk:
            from django.utils import timezone
            self.fields['route_date'].initial = timezone.now().date()

    def clean_route_date(self):
        route_date = self.cleaned_data.get('route_date')

        if route_date:
            from django.utils import timezone
            from datetime import timedelta

            # السماح بالتخطيط حتى 90 يوم في المستقبل
            max_future_date = timezone.now().date() + timedelta(days=90)
            if route_date > max_future_date:
                raise forms.ValidationError('لا يمكن التخطيط لأكثر من 90 يوم في المستقبل')

            # منع التخطيط لأكثر من 7 أيام في الماضي
            min_past_date = timezone.now().date() - timedelta(days=7)
            if route_date < min_past_date:
                raise forms.ValidationError('لا يمكن إنشاء رحلة لأكثر من 7 أيام في الماضي')

        return route_date

    def clean_estimated_duration(self):
        duration_str = self.cleaned_data.get('estimated_duration')

        if duration_str:
            try:
                from datetime import timedelta
                # تحويل النص إلى timedelta
                if isinstance(duration_str, str):
                    # تنسيق: HH:MM:SS
                    parts = duration_str.split(':')
                    if len(parts) == 3:
                        hours, minutes, seconds = map(int, parts)
                        duration = timedelta(hours=hours, minutes=minutes, seconds=seconds)
                        return duration
                    else:
                        raise forms.ValidationError('تنسيق المدة يجب أن يكون HH:MM:SS')
                return duration_str
            except ValueError:
                raise forms.ValidationError('تنسيق المدة غير صحيح. استخدم HH:MM:SS')

        return duration_str

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        driver = cleaned_data.get('driver')
        route_date = cleaned_data.get('route_date')
        planned_start_time = cleaned_data.get('planned_start_time')
        planned_end_time = cleaned_data.get('planned_end_time')

        # التحقق من توفر المركبة والسائق
        if vehicle and driver and route_date and planned_start_time:
            # التحقق من عدم تعارض الرحلات
            conflicting_routes = DailyRoute.objects.filter(
                vehicle=vehicle,
                route_date=route_date,
                status__in=['planned', 'in_progress']
            ).exclude(pk=self.instance.pk if self.instance else None)

            for route in conflicting_routes:
                # التحقق من تداخل الأوقات
                if (planned_start_time >= route.planned_start_time and
                    planned_start_time <= route.planned_end_time):
                    raise forms.ValidationError(
                        f'يوجد تعارض مع رحلة أخرى: {route.route_name} '
                        f'({route.planned_start_time} - {route.planned_end_time})'
                    )

        # التحقق من منطقية الأوقات
        if planned_start_time and planned_end_time:
            if planned_start_time >= planned_end_time:
                raise forms.ValidationError('وقت البداية يجب أن يكون قبل وقت النهاية')

        return cleaned_data


class RouteWaypointForm(forms.ModelForm):
    """نموذج النقاط الوسطية في الرحلة"""

    class Meta:
        model = RouteWaypoint
        fields = [
            'name', 'waypoint_type', 'order', 'address', 'latitude', 'longitude',
            'planned_arrival_time', 'planned_departure_time',
            'contact_person', 'contact_phone', 'special_instructions'
        ]

        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'محطة وقود أرامكو'
            }),
            'waypoint_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الرياض - طريق الملك فهد'
            }),
            'latitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '24.7136',
                'step': '0.00000001'
            }),
            'longitude': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '46.6753',
                'step': '0.00000001'
            }),
            'planned_arrival_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'planned_departure_time': forms.TimeInput(attrs={
                'class': 'form-control',
                'type': 'time'
            }),
            'contact_person': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أحمد محمد'
            }),
            'contact_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '0501234567'
            }),
            'special_instructions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'تعليمات خاصة للنقطة'
            }),
        }

        labels = {
            'name': 'اسم النقطة',
            'waypoint_type': 'نوع النقطة',
            'order': 'ترتيب النقطة',
            'address': 'العنوان',
            'latitude': 'خط العرض',
            'longitude': 'خط الطول',
            'planned_arrival_time': 'الوقت المخطط للوصول',
            'planned_departure_time': 'الوقت المخطط للمغادرة',
            'contact_person': 'الشخص المسؤول',
            'contact_phone': 'رقم الهاتف',
            'special_instructions': 'تعليمات خاصة',
        }

    def clean(self):
        cleaned_data = super().clean()
        planned_arrival_time = cleaned_data.get('planned_arrival_time')
        planned_departure_time = cleaned_data.get('planned_departure_time')

        # التحقق من منطقية الأوقات
        if planned_arrival_time and planned_departure_time:
            if planned_arrival_time >= planned_departure_time:
                raise forms.ValidationError('وقت الوصول يجب أن يكون قبل وقت المغادرة')

        return cleaned_data


# نموذج مجموعة للنقاط الوسطية
RouteWaypointFormSet = forms.inlineformset_factory(
    DailyRoute,
    RouteWaypoint,
    form=RouteWaypointForm,
    extra=1,
    can_delete=True,
    min_num=0,
    max_num=10
)
