<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المركبة - إدارة الأسطول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center">إدارة الأسطول</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fleet:vehicle_list' %}">
                                <i class="fas fa-truck"></i> المركبات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تفاصيل المركبة: {{ vehicle.plate_number }}</h1>
                    <div>
                        <a href="{% url 'fleet:vehicle_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        <button class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                    </div>
                </div>

                <!-- معلومات المركبة الأساسية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>رقم اللوحة:</strong></td>
                                        <td>{{ vehicle.plate_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الموديل:</strong></td>
                                        <td>{{ vehicle.model }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>النوع:</strong></td>
                                        <td>
                                            {% if vehicle.vehicle_type == 'goods' %}
                                                نقل البضائع
                                            {% elif vehicle.vehicle_type == 'people' %}
                                                نقل الأشخاص
                                            {% else %}
                                                مركبة خاصة
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>
                                            {% if vehicle.status == 'active' %}
                                                <span class="badge bg-success">نشط</span>
                                            {% elif vehicle.status == 'maintenance' %}
                                                <span class="badge bg-warning">تحت الصيانة</span>
                                            {% else %}
                                                <span class="badge bg-secondary">غير نشط</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>السائق:</strong></td>
                                        <td>
                                            {% if vehicle.driver %}
                                                {{ vehicle.driver.user.first_name }} {{ vehicle.driver.user.last_name }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>السعة:</strong></td>
                                        <td>{{ vehicle.capacity|default:"غير محدد" }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-alt"></i> معلومات الصيانة والتأمين</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>آخر صيانة:</strong></td>
                                        <td>{{ vehicle.last_maintenance|default:"غير محدد" }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>انتهاء التأمين:</strong></td>
                                        <td>{{ vehicle.insurance_expiry|default:"غير محدد" }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجلات الصيانة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> سجلات الصيانة</h5>
                    </div>
                    <div class="card-body">
                        {% if maintenance_records %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ المجدول</th>
                                            <th>الوصف</th>
                                            <th>الحالة</th>
                                            <th>التكلفة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for maintenance in maintenance_records %}
                                        <tr>
                                            <td>{{ maintenance.scheduled_date }}</td>
                                            <td>{{ maintenance.description }}</td>
                                            <td>
                                                {% if maintenance.status == 'completed' %}
                                                    <span class="badge bg-success">مكتملة</span>
                                                {% elif maintenance.status == 'in_progress' %}
                                                    <span class="badge bg-warning">قيد التنفيذ</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">مجدولة</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ maintenance.cost|default:"غير محدد" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد سجلات صيانة</p>
                        {% endif %}
                    </div>
                </div>

                <!-- الرحلات الأخيرة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-route"></i> الرحلات الأخيرة</h5>
                    </div>
                    <div class="card-body">
                        {% if trips %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>من</th>
                                            <th>إلى</th>
                                            <th>وقت البداية</th>
                                            <th>الحالة</th>
                                            <th>السائق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for trip in trips|slice:":10" %}
                                        <tr>
                                            <td>{{ trip.start_location }}</td>
                                            <td>{{ trip.end_location }}</td>
                                            <td>{{ trip.start_time }}</td>
                                            <td>
                                                {% if trip.status == 'completed' %}
                                                    <span class="badge bg-success">مكتملة</span>
                                                {% elif trip.status == 'in_progress' %}
                                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ trip.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ trip.driver.user.first_name }} {{ trip.driver.user.last_name }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد رحلات مسجلة</p>
                        {% endif %}
                    </div>
                </div>

                <!-- الحوادث -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> الحوادث</h5>
                    </div>
                    <div class="card-body">
                        {% if incidents %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>الموقع</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for incident in incidents %}
                                        <tr>
                                            <td>{{ incident.date_time }}</td>
                                            <td>{{ incident.description|truncatewords:10 }}</td>
                                            <td>{{ incident.location }}</td>
                                            <td>
                                                {% if incident.status == 'resolved' %}
                                                    <span class="badge bg-success">محلول</span>
                                                {% elif incident.status == 'investigating' %}
                                                    <span class="badge bg-warning">قيد التحقيق</span>
                                                {% else %}
                                                    <span class="badge bg-danger">مبلغ عنه</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">لا توجد حوادث مسجلة</p>
                        {% endif %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
